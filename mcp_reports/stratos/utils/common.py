import imp
import json
import logging
import os
import subprocess

import pendulum
from pybars import Compiler

from mcp_reports.stratos.config.config import REPORT_CONFIG_DIR, QUERY_TEMPLATES_DIR, \
    EMAIL_TEMPLATES_DIR, TRANSFORMERS_DIR, HDFS_USER_ID, DATE_RANGE_CONFIG, CONTEXTS_DIR


def apply_template(template_data, template_context):
    compiler = Compiler()
    template = compiler.compile(template_data.decode())
    return ''.join(template(template_context))


def determine_execution_date(**kwargs):
    if kwargs['execution_date'].second != 0:
        # Manual run
        execution_date = kwargs['prev_execution_date']
    else:
        # Scheduled or backfill run
        execution_date = kwargs['next_execution_date']

    return execution_date


def get_date_range(execution_date, report_config):
    days_before_curr_date = DATE_RANGE_CONFIG.get(report_config['name'], {}) \
        .get('days_before_curr_date', 1)
    end_date = execution_date.subtract(days=days_before_curr_date).end_of('day')

    duration_in_days = DATE_RANGE_CONFIG.get(report_config['name'], {}) \
        .get('report_duration_in_days', 1)
    start_date = end_date.subtract(days=duration_in_days - 1).start_of('day')

    return start_date, end_date


def get_formatted_date(d):
    return d.format('YYYY-MM-DD')


def format_query(query, start_date, end_date, execution_date):
    period = pendulum.period(start_date, end_date)
    month_set = set([d.month for d in period.range('days')])
    year_set = set([d.year for d in period.range('days')])

    context = {
        'YEARS': ",".join(str(year) for year in year_set),
        'MONTHS': ",".join(str(month) for month in month_set),
        'START_DATE': get_formatted_date(start_date),
        'START_TIME': start_date,
        'START_DAY': start_date.day,
        'START_MONTH': start_date.month,
        'START_YEAR': start_date.year,
        'END_DATE': get_formatted_date(end_date),
        'END_TIME': end_date,
        'END_DAY': end_date.day,
        'END_MONTH': end_date.month,
        'END_YEAR': end_date.year,
        'EXECUTION_DATE': get_formatted_date(execution_date),
        'EXECUTION_TIME': execution_date,
        'EXECUTION_DAY': execution_date.day,
        'EXECUTION_MONTH': execution_date.month,
        'EXECUTION_YEAR': execution_date.year
    }

    return apply_template(query.encode(), context)


def read_report_config(filename):
    config_filepath = os.path.join(REPORT_CONFIG_DIR, filename)
    report_config = json.load(open(config_filepath))
    logging.info("Report config ({})".format(report_config))
    return report_config


def read_query_template(query_template_filename):
    query_template_filepath = os.path.join(QUERY_TEMPLATES_DIR, query_template_filename)
    return open(query_template_filepath).read()


def read_email_template(email_template_filename):
    email_template_filepath = os.path.join(EMAIL_TEMPLATES_DIR, email_template_filename)
    return open(email_template_filepath).read()


def create_dir_for_file(file_path):
    dir_path = os.path.dirname(file_path)
    if not os.path.exists(dir_path):
        os.umask(0)
        os.makedirs(dir_path)


def run_cmd(cmd):
    logging.info("Running subprocess: cmd({})".format(cmd))
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True)
    stdout, _ = process.communicate()
    return_code = process.returncode
    if return_code == 0:
        logging.info("Running subprocess completed: stdout({})".format(stdout))
        return stdout
    else:
        raise Exception(
            "Running subprocess failed: return_code({}), stdout({})".format(return_code, stdout))


def append_to_file_top(file_path, content):
    cmd = "echo '{content}' | cat - '{file_path}' > '{temp_path}' && mv '{temp_path}' '{file_path}'".format(
        content=content,
        file_path=file_path,
        temp_path="{}.swp".format(file_path)
    )
    run_cmd(cmd)


def remove_file_pattern(file_path_pattern):
    cmd = "rm -rf {file_path_pattern}".format(file_path_pattern=file_path_pattern)
    run_cmd(cmd)


def remove_file_pattern_using_airflow_user(file_path_pattern):
    cmd = "sudo -u {airflow_user} rm -rf {file_path_pattern}".format(airflow_user=HDFS_USER_ID,
                                                                     file_path_pattern=
                                                                     file_path_pattern)
    run_cmd(cmd)


def line_count(file_path):
    cmd = "wc -l {file_path}".format(file_path=file_path)
    return int(run_cmd(cmd).decode().strip().split(' ')[0].strip())


def write_units_to_file(tsv_file_path, csv_file_path, python_file_name):
    logging.info("python file path is {}".format(TRANSFORMERS_DIR + "/" + python_file_name))
    processor = imp.load_source("", TRANSFORMERS_DIR + "/" + python_file_name)
    processor.process(tsv_file_path, csv_file_path)


def convert_tsv_to_report_format(tsv_file_path, csv_file_path, python_file_name):
    write_units_to_file(tsv_file_path, csv_file_path, python_file_name)


def get_context(python_file_name, csv_file_path, start_date, end_date, execution_date):
    logging.info("python file path is {}".format(CONTEXTS_DIR + "/" + python_file_name))
    processor = imp.load_source("", CONTEXTS_DIR + "/" + python_file_name)
    return processor.build_context(csv_file_path, start_date, end_date, execution_date)
