{"name": "stratos-chargeback-summary-ytd-report", "description": "Stratos Chargeback Summary YTD Report", "version": 1, "fileName": "STRATOS_CHARGEBACK_SUMMARY_YTD_REPORT_FROM_{START_DATE}_TO_{END_DATE}.csv", "fileType": ".csv", "query": {"source": "hive", "templatePath": ["chargeback_summary_ytd_query.hql"]}, "transformer": "chargeback_summary_ytd_report_transformer.py", "columns": ["File Id", "Chargeback Id", "Chargeback Workflow Id", "Chargeback Workflow Version", "Chargeback Type", "Chargeback Category", "Chargeback Stage", "Payment Reference Id", "Merchant Id", "Merchant Transaction Id", "UPI/PG Transaction Id", "Dispute Reference ID", "RRN", "FreshDesk Ticket ID", "Original Transaction Amount", "Chargeback Amount", "Accepted Amount", "Penalty Amount", "Current State", "Current Event", "Chargeback Issuer", "Raised At", "Respond By", "Chargeback Source Type", "User Id", "Chargeback Created At", "Chargeback Updated At"], "schedule": "30 3 * * 2", "email": {"to": ["<EMAIL>", "<EMAIL>"], "subject": "Stratos Chargeback Summary YTD Report From {START_DATE} To {END_DATE}", "templatePath": "chargeback_summary_ytd_report.html"}, "isActive": true, "uploadToDocstore": {"aliasId": "tFez3Y8B6q-o1NToquFR"}}