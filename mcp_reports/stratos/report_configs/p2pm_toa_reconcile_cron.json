{"name": "stratos-p2pm-toa-reconcile-cron", "description": "Stratos P2PM TOA Reconcile Cron", "version": 1, "fileName": "STRATOS_P2PM_TOA_RECONCILE_CRON_{START_DATE}.csv", "fileType": ".csv", "query": {"source": "hive", "templatePath": ["p2pm_toa_reconcile_query.hql"]}, "transformer": "p2pm_toa_reconcile_transformer.py", "columns": ["Transaction Reference Id", "Dispute Workflow id"], "schedule": "0 */6 * * *", "email": {"to": ["<EMAIL>"], "subject": "Stratos P2PM Toa Reconcile Failure", "templatePath": "p2pm_toa_reconcile.html"}, "isActive": true}