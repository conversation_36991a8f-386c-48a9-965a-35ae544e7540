{"name": "stratos-pg-chargeback-aging-alert", "description": "Stratos PG Chargeback Aging <PERSON>", "version": 1, "fileName": "STRATOS_PG_CHARGEBACK_AGING_ALERT_{START_DATE}.csv", "fileType": ".csv", "query": {"source": "hive", "templatePath": ["pg_chargeback_aging_alert_query.hql"]}, "transformer": "chargeback_aging_alert.py", "columns": ["Dispute Source Id", "Dispute Type", "Dispute Stage", "Transaction Reference Id", "Current State", "Raised At", "Created At", "Updated At"], "schedule": "30 1 * * 1", "email": {"to": ["<EMAIL>"], "subject": "Stratos PG Chargeback Credit/Debit Received Action Items - {START_DATE}", "templatePath": "pg_chargeback_aging_alert.html"}, "isActive": true}