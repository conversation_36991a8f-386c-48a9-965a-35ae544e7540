{"name": "stratos-upi-chargeback-aging-alert", "description": "Stratos UPI Chargeback A<PERSON>", "version": 1, "fileName": "STRATOS_UPI_CHARGEBACK_AGING_ALERT_{START_DATE}.csv", "fileType": ".csv", "query": {"source": "hive", "templatePath": ["upi_chargeback_aging_alert_query.hql"]}, "transformer": "chargeback_aging_alert.py", "columns": ["Dispute Source Id", "Dispute Type", "Dispute Stage", "Transaction Reference Id", "Current State", "Raised At", "Created At", "Updated At"], "schedule": "30 1 * * *", "email": {"to": ["<EMAIL>", "<EMAIL>"], "subject": "Stratos UPI Chargeback Credit/Debit Received Action Items - {START_DATE}", "templatePath": "upi_chargeback_aging_alert.html"}, "isActive": true}