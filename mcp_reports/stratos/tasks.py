import logging
import uuid
import traceback

from mcp_reports.stratos.zencast_client import ZencastClient

from hive.hook.secured_hive_cli_hook import SecuredHiveC<PERSON>Hook

from mcp_reports.stratos.config.config import HDFS_USER_ID, REALM, TSV_FILE_NAME_PATTERN, \
    LOCAL_TEMP_DIR, HDFS_TEMP_DIR, DYNAMIC_REPORT_CONFIG
from mcp_reports.stratos.utils.common import read_query_template, format_query, get_date_range, \
    get_formatted_date, append_to_file_top, apply_template, read_email_template, \
    convert_tsv_to_report_format, remove_file_pattern, remove_file_pattern_using_airflow_user, \
    line_count, determine_execution_date, get_context
from mcp_reports.stratos.utils.docstore import docstore_links
from mcp_reports.stratos.utils.hdfs import delete_from_hdfs, create_hdfs_dir, \
    concat_hdfs_to_local, concat_local_to_hdfs


def get_file_paths(report_name, file_name, start_date, end_date, execution_date, query):
    tsv_file_name = TSV_FILE_NAME_PATTERN.format(REPORT_NAME=report_name,
                                                 START_DATE=get_formatted_date(start_date),
                                                 END_DATE=get_formatted_date(end_date),
                                                 EXECUTION_DATE=get_formatted_date(
                                                     execution_date),
                                                 QUERY=query)

    report_file_name = file_name.format(
        START_DATE=get_formatted_date(start_date),
        END_DATE=get_formatted_date(end_date))

    hdfs_tsv_file_path = HDFS_TEMP_DIR + tsv_file_name
    hdfs_report_file_path = HDFS_TEMP_DIR + report_file_name

    local_tsv_file_path = LOCAL_TEMP_DIR + tsv_file_name
    local_report_file_path = LOCAL_TEMP_DIR + report_file_name

    return hdfs_tsv_file_path, hdfs_report_file_path, local_tsv_file_path, local_report_file_path


def execute_hive_query(report_config, **kwargs):
    execution_date = determine_execution_date(**kwargs)
    start_date, end_date = get_date_range(execution_date, report_config)

    for template_path in report_config['query']['templatePath']:
        hdfs_tsv_file_path, _, _, _ = get_file_paths(report_config['name'],
                                                     report_config['fileName'],
                                                     start_date, end_date, execution_date,
                                                     template_path)
        create_hdfs_dir(HDFS_TEMP_DIR)

        query_template = read_query_template(template_path)
        hql = format_query(query_template, start_date, end_date, execution_date)
        hql = """
        set tez.queue.name = default; 
        set hive.exec.compress.output=false; 
        set hive.merge.tezfiles=true;
        INSERT OVERWRITE DIRECTORY '{output_file}' ROW FORMAT DELIMITED FIELDS TERMINATED BY 
        '\t' STORED AS TEXTFILE
        """.format(output_file=hdfs_tsv_file_path) + hql

        logging.info("Hql Query: ({})".format(hql))

        hive_hook = SecuredHiveCliHook(user=HDFS_USER_ID, realm=REALM)
        hive_hook.run_cli(hql)


def generate_output_report(report_config, **kwargs):
    execution_date = determine_execution_date(**kwargs)
    logging.info('kwargs is: ({})'.format(kwargs))
    start_date, end_date = get_date_range(execution_date, report_config)

    columns = ",".join(report_config['columns'])
    if report_config.get('fileType', '.csv') == '.psv':
        columns = "|".join(report_config['columns'])

    local_report_file_path = ''
    hdfs_report_file_path = ''
    clean_up_csv_if_present = True

    for template_path in report_config['query']['templatePath']:

        hdfs_tsv_file_path, hdfs_report_file_path, local_tsv_file_path, local_report_file_path, \
            = get_file_paths(report_config['name'],
                             report_config['fileName'],
                             start_date, end_date, execution_date,
                             template_path)

        if clean_up_csv_if_present:
            remove_file_pattern(local_report_file_path)
            remove_file_pattern_using_airflow_user(local_tsv_file_path)
            clean_up_csv_if_present = False

        concat_hdfs_to_local(hdfs_tsv_file_path, local_tsv_file_path)
        convert_tsv_to_report_format(local_tsv_file_path, local_report_file_path,
                                     report_config.get('transformer', 'default.py'))
        delete_from_hdfs(hdfs_tsv_file_path)
        remove_file_pattern_using_airflow_user(local_tsv_file_path)

    append_to_file_top(local_report_file_path, columns)
    concat_local_to_hdfs(local_report_file_path, hdfs_report_file_path)
    remove_file_pattern(local_report_file_path)

def upload_to_docstore(report_config, **kwargs):
    execution_date = determine_execution_date(**kwargs)
    start_date, end_date = get_date_range(execution_date, report_config)

    csv_paths = []
    for template_path in report_config['query']['templatePath']:
        _, hdfs_report_file_path, _, local_report_file_path, \
            = get_file_paths(report_config['name'],
                             report_config['fileName'],
                             start_date, end_date, execution_date,
                             template_path)

        concat_hdfs_to_local(hdfs_report_file_path, local_report_file_path)
        csv_paths.append(local_report_file_path)

    alias_id = report_config['uploadToDocstore']['aliasId']
    logging.info("Generating docstore links for {}".format(csv_paths))
    links = docstore_links(alias_id, csv_paths)
    logging.info("Generated docstore links: {}".format(links))
    kwargs['ti'].xcom_push("docstore_links", links)

    logging.info("Cleaning temp_csv_local_path :({})".format(local_report_file_path))
    remove_file_pattern_using_airflow_user(local_report_file_path)



def read_local_file(file_path):
    with open(file_path, "rb") as fil:
        return fil.read()

def email_generated_report(report_config, **kwargs):
    if not report_config.get('email'):
        logging.info("Skipping email. No email config found.")
        return

    email_config = report_config['email']
    execution_date = determine_execution_date(**kwargs)
    start_date, end_date = get_date_range(execution_date, report_config)

    _, hdfs_report_file_path, _, local_report_file_path = get_file_paths(report_config['name'],
                                                                         report_config['fileName'],
                                                                         start_date, end_date,
                                                                         execution_date, None)
    concat_hdfs_to_local(hdfs_report_file_path, local_report_file_path)

    if line_count(local_report_file_path) == 1:
        logging.info("Skipping email. No records present.")
        remove_file_pattern_using_airflow_user(local_report_file_path)
        delete_from_hdfs(hdfs_report_file_path)
        return
    
    context = get_context(report_config.get('context', 'default.py'), local_report_file_path, start_date, end_date,
                          execution_date)
    dynamic_report_config =  DYNAMIC_REPORT_CONFIG[report_config['name']]
    if 'docstore' in dynamic_report_config and dynamic_report_config['docstore']:
        logging.info(f"Pulling docstore links for {report_config['name']} from XCom")
        docstore_links = ""
        try:
            docstore_links = kwargs['ti'].xcom_pull(task_ids="upload_to_docstore", key="docstore_links")
            logging.info("checking for empty docstore link: {}".format(docstore_links[0]))
            logging.info("docstore links: {}".format(docstore_links))
        except Exception:
            logging.error("Pull failed for docstore links")

        
        context.update({
            'EXECUTION_DATE': get_formatted_date(execution_date),
            'DOCSTORE_LINKS': docstore_links
        })
    if 'email' in dynamic_report_config and len(dynamic_report_config['email']) > 0:
        recipients = dynamic_report_config['email'] 
    else:
        recipients =  email_config['to']
    subject = email_config['subject'].format(**context)

    content = apply_template(read_email_template(
        email_config.get('templatePath', 'default.html')
    ).encode(), context)


    logging.info("Generating requestId for sending email via Zencast")
    request_id = str(uuid.uuid1())

    logging.info("Initializing Zencast client")
    zencast_client = ZencastClient()
    file_name = report_config['fileName'].format(**context)

    try:
        logging.info("Sending email with content %s", content)
        if 'docstore' in dynamic_report_config and dynamic_report_config['docstore']:
            zencast_client.send_email(request_id, recipients, str(content), subject, file_name, None)
        else:
            zencast_client.send_email(request_id, recipients, str(content), subject, file_name, read_local_file(local_report_file_path))
    except Exception:
        logging.error("Failed to send E-Mail. Exception: %s" % traceback.format_exc())

    remove_file_pattern_using_airflow_user(local_report_file_path)
    delete_from_hdfs(hdfs_report_file_path)
