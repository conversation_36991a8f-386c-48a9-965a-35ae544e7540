from airflow.operators.python_operator import PythonOperator

from mcp_reports.stratos.tasks import execute_hive_query, generate_output_report, \
    email_generated_report, upload_to_docstore


def execute_hive_query_operator(dag, report_config):
    return PythonOperator(
        task_id='execute_hive_query',
        provide_context=True,
        python_callable=execute_hive_query,
        op_kwargs={
            'report_config': report_config,
        },
        dag=dag,
    )


def generate_output_report_operator(dag, report_config):
    return PythonOperator(
        task_id='generate_output_report',
        provide_context=True,
        python_callable=generate_output_report,
        op_kwargs={
            'report_config': report_config,
        },
        dag=dag,
    )

def upload_to_docstore_operator(dag, report_config):
    return PythonOperator(
        task_id='upload_to_docstore',
        provide_context=True,
        python_callable=upload_to_docstore,
        op_kwargs={
            'report_config': report_config,
        },
        dag=dag,
    )


def email_generated_report_operator(dag, report_config):
    return PythonOperator(
        task_id='email_generated_report',
        provide_context=True,
        python_callable=email_generated_report,
        op_kwargs={
            'report_config': report_config,
        },
        dag=dag,
    )
