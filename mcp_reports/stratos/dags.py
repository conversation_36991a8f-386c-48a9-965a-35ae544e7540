import os
from datetime import timed<PERSON><PERSON>

import pendulum
from airflow import DAG

from mcp_reports.stratos.config.config import REPORT_CONFIG_DIR, DYNAMIC_REPORT_CONFIG
from mcp_reports.stratos.operators import execute_hive_query_operator, \
    generate_output_report_operator, upload_to_docstore_operator, email_generated_report_operator
from mcp_reports.stratos.utils.common import read_report_config

AIRFLOW_DEFAULT_ARGS = {
    'owner': 'phonepe_mcp_airflow_user',
    'depends_on_past': False,
    'email': ['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': True,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
    'start_date': pendulum.datetime(2021, 10, 1)
}


def generate_dag(dag_id, report_config):
    return DAG(
        dag_id,
        default_args=AIRFLOW_DEFAULT_ARGS,
        description=report_config.get('description', report_config['name']),
        schedule_interval=report_config['schedule'],
        catchup=False,
    )


def configure_dag(dag, report_config):
    query_source = report_config['query']['source']
    if query_source == 'hive':
        execute_query_op = execute_hive_query_operator(dag, report_config)
    else:
        raise Exception('Invalid query source supplied')

    generate_report_op = generate_output_report_operator(dag, report_config)

    dynamic_report_config =  DYNAMIC_REPORT_CONFIG[report_config['name']]
    if report_config.get('email'):
        email_report_op = email_generated_report_operator(dag, report_config)
        if 'docstore' in dynamic_report_config and dynamic_report_config['docstore']:
            upload_docstore_op = upload_to_docstore_operator(dag, report_config)
            execute_query_op >> generate_report_op >> upload_docstore_op >> email_report_op
        else:
            execute_query_op >> generate_report_op >> email_report_op
    else:
        execute_query_op >> generate_report_op


def main():
    report_config_files = os.listdir(REPORT_CONFIG_DIR)
    report_configs = [read_report_config(rcf) for rcf in report_config_files]

    for report_config in report_configs:
        dag_id = '{}_v{}'.format(report_config['name'], report_config.get('version', 1))
        dag = generate_dag(dag_id, report_config)
        configure_dag(dag, report_config)
        globals()[dag_id] = dag


main()
