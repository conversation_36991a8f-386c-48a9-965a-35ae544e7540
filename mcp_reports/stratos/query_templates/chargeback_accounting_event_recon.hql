SELECT stratos.eventdata_chargebackaccountingeventid
FROM   foxtrot_stream.stratos_accounting_events AS stratos
WHERE  stratos.eventtype = 'CHARGEBACK_ACCOUNTING_EVENT'
       AND stratos.eventdata_accountingeventstatus = 'RAISED'
       AND stratos.year = {{START_YEAR}}
       AND stratos.month = {{START_MONTH}}
       AND stratos.day = {{START_DAY}}
       AND stratos.eventdata_chargebackaccountingeventid NOT IN (SELECT plts.eventdata_header_transactionid
                       FROM   foxtrot_stream.plutus_backend_statechange AS plts
                       WHERE  plts.eventtype in ('LEDGER_EVENT_PROCESSED', 'LEDGER_EVENT_SIDELINED')
                       AND    plts.eventdata_header_eventtype in ('UPI_CHARGEBACK_RECOVERY','UPI_CHARGEBACK_RECOVERY_REVERSAL','UPI_CHARGEBACK_PENALTY_RECOVERY','PG_CHARGEBACK_RECOVERY','PG_CHARGEBACK_RECOVERY_REVERSAL','PG_CHARGEBACK_PENALTY_RECOVERY')
                       AND    plts.year = {{START_YEAR}}
                       AND    plts.month = {{START_MONTH}}
                       AND    plts.day = {{START_DAY}})