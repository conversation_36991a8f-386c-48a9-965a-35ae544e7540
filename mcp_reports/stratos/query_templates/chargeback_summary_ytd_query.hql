SELECT  dw.dispute_source_id,
        d.dispute_id,
        dw.dispute_workflow_id,
        dw.dispute_workflow_version,
        dw.dispute_type,
        d.dispute_category,
        dw.dispute_stage,
        dw.transaction_reference_id,
        d.merchant_id,
        d.merchant_transaction_id,
        d.instrument_transaction_id,
        d.dispute_reference_id,
        d.rrn,
        dw.communication_id,
        d.transaction_amount,
        dw.disputed_amount,
        dw.accepted_amount,
        dw.penalty_amount,
        dw.current_state,
        dw.current_event,
        d.dispute_issuer,
        dw.raised_at,
        dw.respond_by,
        dw.dispute_source_type,
        dw.gandalf_user_id,
        dw.created_at,
        dw.updated_at
FROM    stratos.dispute_workflow as dw join stratos.dispute as d on (d.transaction_reference_id=dw.transaction_reference_id)
WHERE   dw.dispute_type in (0,1) AND dw.raised_at > add_months(current_date(),-12);