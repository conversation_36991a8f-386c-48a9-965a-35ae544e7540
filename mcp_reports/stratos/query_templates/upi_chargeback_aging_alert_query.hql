SELECT dw.dispute_source_id,
       dw.dispute_type,
       dw.dispute_stage,
       dw.transaction_reference_id,
       dw.current_state,
       dw.raised_at,
       dw.created_at,
       dw.updated_at  from stratos.dispute_workflow as dw
       WHERE  ((dw.current_state in (4) AND  dw.dispute_stage in (0)) OR  (dw.current_state in (9,13) AND  dw.dispute_stage in (1)))
       AND dw.dispute_type in (0)
       AND dw.raised_at >=date_add(current_date, -11)
       AND dw.raised_at <=date_add(current_date, -4);