import os
import json

from mcp_reports.stratos.utils.rosey import get_rosey_config_cached, is_prod_env

PROJECT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))

ENV = os.environ.get('ENV', 'stage')
GLOBAL_CONFIG_FILE = 'config/config.json' if is_prod_env(ENV) else 'config/local_config.json'
GLOBAL_CONFIG = json.load(open(os.path.join(PROJECT_DIR, GLOBAL_CONFIG_FILE)))
REPORT_CONFIG_DIR = os.path.join(PROJECT_DIR, 'report_configs')
QUERY_TEMPLATES_DIR = os.path.join(PROJECT_DIR, 'query_templates')
EMAIL_TEMPLATES_DIR = os.path.join(PROJECT_DIR, 'email_templates')
TRANSFORMERS_DIR = os.path.join(PROJECT_DIR, 'transformers')
CONTEXTS_DIR = os.path.join(PROJECT_DIR, 'contexts')

ROSEY_CONFIG_PATH = os.path.join(PROJECT_DIR, GLOBAL_CONFIG['ROSEY_CONFIG_PATH'])
ROSEY_CONFIG = get_rosey_config_cached(ENV, ROSEY_CONFIG_PATH, 'mcp', 'stratosReports')

LOCAL_TEMP_DIR = ROSEY_CONFIG['LOCAL_TEMP_DIR']
HDFS_TEMP_DIR = ROSEY_CONFIG['HDFS_TEMP_DIR']
TSV_FILE_NAME_PATTERN = ROSEY_CONFIG['TSV_FILE_NAME_PATTERN']
REALM = ROSEY_CONFIG['REALM']

OLYMPUS_PROTOCOL = ROSEY_CONFIG['OLYMPUS_PROTOCOL']
OLYMPUS_HOST = ROSEY_CONFIG['OLYMPUS_HOST']
OLYMPUS_PORT = ROSEY_CONFIG['OLYMPUS_PORT']
OLYMPUS_CLIENT_ID = ROSEY_CONFIG['OLYMPUS_CLIENT_ID']
OLYMPUS_CLIENT_KEY = ROSEY_CONFIG['OLYMPUS_CLIENT_KEY']


ZENCAST_PROTOCOL = ROSEY_CONFIG['ZENCAST_PROTOCOL']
ZENCAST_HOST = ROSEY_CONFIG['ZENCAST_HOST']
ZENCAST_PORT = ROSEY_CONFIG['ZENCAST_PORT']

STRATOS_BASE_URL = ROSEY_CONFIG['STRATOS_BASE_URL']

DOCSTORE_CONFIG = ROSEY_CONFIG['DOCSTORE_CONFIG']
DEFAULT_ALIAS_ID = DOCSTORE_CONFIG['DEFAULT_ALIAS_ID']
NAMESPACE = DOCSTORE_CONFIG['DOCSTORE_NAMESPACE']
DOCSTORE_URL = DOCSTORE_CONFIG['DOCSTORE_URL']
DOCSTORE_UI_URL = DOCSTORE_CONFIG['DOCSTORE_UI_URL']
DOCSTORE_BASE_URL = DOCSTORE_CONFIG['DOCSTORE_BASE_URL']
DOCSTORE_TTL = DOCSTORE_CONFIG['DOCSTORE_TTL']

DYNAMIC_REPORT_CONFIG = ROSEY_CONFIG['REPORT_CONFIG']

HDFS_USER_ID = ROSEY_CONFIG['HDFS_USER_ID']
HDFS_CMD = ROSEY_CONFIG['HDFS_CMD']

DATE_RANGE_CONFIG = ROSEY_CONFIG.get('DATE_RANGE_CONFIG', {})

