import csv
import logging
from mcp_reports.stratos.stratos_client import StratosClient




def process(tsv_file_path, csv_file_path):

    stratos_client = StratosClient()

    with open(csv_file_path, 'w') as csvfile:
        writer = csv.writer(csvfile, delimiter=',', quotechar='|', quoting=csv.QUOTE_MINIMAL)
        tsv_file = open(tsv_file_path)
        rows = list(csv.reader(tsv_file, delimiter='\t'))
        logging.info("Number of rows found are {}".format(len(rows)))

        for row in rows:
            try:
                res = stratos_client.p2pm_reconcile(row[0], row[1])
                if res.status_code != 200:
                    writer.writerow(row)
            except Exception as e:
                logging.error("Failed to reconcile: {}".format(e))
