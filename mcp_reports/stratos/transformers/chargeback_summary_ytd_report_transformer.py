import csv
import logging

from mcp_reports.stratos.stratos_client import StratosClient


def process(tsv_file_path, csv_file_path):

    global response_map

    stratos_client = StratosClient()
    try:
        res = stratos_client.get_enums("")
        response_map = res.json()
    except Exception as e:
        logging.error("Failed to get enum values: {}".format(e))
        raise e

    logging.info("Successfully get the enum values: {}".format(response_map))

    with open(csv_file_path, 'w') as csvfile:
        writer = csv.writer(csvfile, delimiter=',', quotechar='|', quoting=csv.QUOTE_MINIMAL)
        tsv_file = open(tsv_file_path)
        rows = list(csv.reader(tsv_file, delimiter='\t'))
        logging.info("Number of rows found are {}".format(len(rows)))

        for row in rows:
            row[3]=response_map.get('DISPUTE_WORKFLOW_VERSION').get(row[3])
            row[4]=response_map.get('DISPUTE_TYPE').get(row[4])
            row[5]=response_map.get('DISPUTE_CATEGORY').get(row[5])
            row[6]=response_map.get('DISPUTE_STAGE').get(row[6])
            row[18]=response_map.get('DISPUTE_WORK_FLOW_STATE').get(row[18])
            row[19]=response_map.get('DISPUTE_WORK_FLOW_EVENT').get(row[19])
            row[20]=response_map.get('DISPUTE_ISSUER').get(row[20])
            row[23]=response_map.get('SOURCE_TYPE').get(row[23])

            writer.writerow(row)


