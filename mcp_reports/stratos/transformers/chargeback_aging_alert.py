import csv
import logging

from mcp_reports.stratos.stratos_client import StratosClient


def process(tsv_file_path, csv_file_path):
    response_map = {}

    stratos_client = StratosClient()
    try:
        res = stratos_client.get_enums("")
        response_map = res.json()
    except Exception as e:
        logging.error("Failed to get enum values: {}".format(e))
        raise e

    with open(csv_file_path, 'w') as csvfile:
        writer = csv.writer(csvfile, delimiter=',', quotechar='|', quoting=csv.QUOTE_MINIMAL)
        tsv_file = open(tsv_file_path)
        rows = list(csv.reader(tsv_file, delimiter='\t'))
        logging.info("Number of rows found are {}".format(len(rows)))

        for row in rows:
            row[1] = response_map.get('DISPUTE_TYPE').get(row[1])
            row[2] = response_map.get('DISPUTE_STAGE').get(row[2])
            row[4] = response_map.get('DISPUTE_WORK_FLOW_STATE').get(row[4])
            writer.writerow(row)
