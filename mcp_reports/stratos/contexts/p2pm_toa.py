import logging
import pandas as pd
from mcp_reports.stratos.utils.common import get_formatted_date

def get_value(data, column):
    if data.empty is False:
        value = data[column].values[0]
    else:
        value = 0
    return value
def build_context(file_name, start_date, end_date, execution_date):


    df = pd.read_csv(file_name)

    logging.info("p2pm toa df :({})".format(df))

    p2pm_toa_completed_data = df[df['Current State'] == 'P2PM_TOA_COMPLETED']
    p2pm_toa_completed_amount = get_value(p2pm_toa_completed_data, 'Total Amount (In Paise)')
    p2pm_toa_completed_externally_data = df[df['Current State'] == 'TOA_COMPLETED_EXTERNALLY']
    p2pm_toa_completed_externally_amount = get_value(p2pm_toa_completed_externally_data, 'Total Amount (In Paise)')
    p2pm_toa_pending_data = df[df['Current State'] == 'P2PM_TOA_PENDING']
    p2pm_toa_pending_count = get_value(p2pm_toa_pending_data, 'Total Count')

    p2mp_toa_failed_after_max_retry_auto_data = df[df['Current State'] == 'P2PM_TOA_FAILED_AFTER_MAX_AUTO_RETRY']
    p2mp_toa_failed_after_max_auto_retry_count = get_value(p2mp_toa_failed_after_max_retry_auto_data, 'Total Count')
    p2pm_toa_processed = round((p2pm_toa_completed_amount + p2pm_toa_completed_externally_amount) / 100, 2)
    action_data = ""

    if p2pm_toa_pending_count >0 or p2mp_toa_failed_after_max_auto_retry_count:
        action_data = "There are some P2PM TOAs which are in pending or failed state. You may take the appropriate action from the Stratos Console Dashboard ( https://stratos-console.phonepe.com/ )."
    context = {
        'START_DATE': get_formatted_date(start_date),
        'END_DATE': get_formatted_date(end_date),
        'EXECUTION_DATE': get_formatted_date(execution_date),
        'P2PM_TOA_PROCESSED': p2pm_toa_processed,
        'ACTION_DATA': action_data
    }
    logging.info("Context built for P2PM TOA :({})".format(context))
    return context
