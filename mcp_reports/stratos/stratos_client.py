
from mcp_reports.stratos.config.config import STRATOS_BASE_URL
from retrying import retry
import requests
import logging

from mcp_reports.stratos.zencast_client import olympusClient

HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

AUTHORIZATION = "Authorization"


class StratosClient(object):

    @retry(stop_max_attempt_number=3, wait_exponential_multiplier=10, wait_exponential_max=1000)
    def get_enums(self, query_params):
        url = STRATOS_BASE_URL + "/v1/dispute/enums" + query_params


        try:
            auth_token = olympusClient.get_bearer_auth_token()
            if auth_token:
                HEADERS[AUTHORIZATION] = auth_token
                response = requests.get(url=url, headers=HEADERS, verify= False)
                if response.status_code / 100 == 2:
                    logging.info("Success get enum Stratos response")
                    return response
                else:
                    logging.info("Stratos get enum, Non success response " + str(response.status_code))
                    logging.info(response.content)
            else:
                logging.info("Auth token received from Olympus is empty")
        except Exception as e:
            logging.error("Unable to get Enum from Stratos (will attempt login 3 times): {}".format(e.message))
            raise e

    @retry(stop_max_attempt_number=3, wait_exponential_multiplier=10, wait_exponential_max=1000)
    def p2pm_reconcile(self, transaction_ref_id, dispute_workflow_id):
        url = STRATOS_BASE_URL + "/v1/toa/P2PM_TOA/reconcile"

        data = {
                  'transactionReferenceId': transaction_ref_id,
                  'disputeWorkflowId': dispute_workflow_id
               }

        try:
            auth_token = olympusClient.get_bearer_auth_token()
            if auth_token:
                HEADERS[AUTHORIZATION] = auth_token
                response = requests.post(url=url, headers=HEADERS, json=data, verify= False)
                if response.status_code / 100 == 2:
                    logging.info("Success Reconcile Response from Stratos")
                else:
                    logging.info("Stratos Reconcile, Non success Response " + str(response.status_code))
                    logging.info(response.content)
                return response
            else:
                logging.info("Auth token received from Olympus is empty")
        except Exception as e:
            logging.error("Unable to reconcile from Stratos (will attempt login 3 times): {}".format(e.message))
            raise e
