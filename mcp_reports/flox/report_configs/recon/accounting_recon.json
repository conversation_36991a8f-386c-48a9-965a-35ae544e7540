{"name": "FLOX_ACCOUNTING_RECON", "fileName": "FLOX_ACCOUNTING_RECON_{EXECUTION_DATE}_{START_DATE}_{END_DATE}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["recon/accounting_recon.hql"]}, "schedule": "0 5 * * *", "columns": ["Payment ID", "Fulfiment ID", "Event Type", "Date"], "email": {"condition": {">": [{"var": "LINE_COUNT"}, 1]}, "to": ["<EMAIL>"], "subject": "FLOX_ACCOUNTING_RECON_{EXECUTION_DATE}_{START_DATE}_{END_DATE}", "templatePath": "accounting_recon.html"}}