import json
import logging
import os
import subprocess


from pybars import Compiler
from zipfile import ZipFile, ZIP_DEFLATED
from mcp_reports.flox.config.config import QUERY_TEMPLATES_DIR, EMAIL_TEMPLATES_DIR, HDFS_USER_ID, DATE_RANGE_CONFIG


def apply_template(template_data, template_context):
    compiler = Compiler()
    logging.info("Template data with encode is : {}".format(template_data))
    logging.info("Template context with encode is : {}".format(template_context))
    template = compiler.compile(template_data.decode())
    return ''.join(template(template_context))


def get_formatted_date(d):
    return d.format('YYYY-MM-DD')


def format_query(query, execution_date, start_date, end_date):
    context = {
        'EXECUTION_DATE': get_formatted_date(execution_date),
        'START_DATE': get_formatted_date(start_date),
        'END_DATE': get_formatted_date(end_date),
    }

    return apply_template(query.encode(), context)


def read_report_config(filename):
    config_filepath = os.path.join(filename)
    report_config = json.load(open(config_filepath))
    logging.info("Report config ({})".format(report_config))
    return report_config


def read_query_template(query_template_filename):
    query_template_filepath = os.path.join(QUERY_TEMPLATES_DIR, query_template_filename)
    return open(query_template_filepath).read()


def read_email_template(email_template_filename):
    email_template_filepath = os.path.join(EMAIL_TEMPLATES_DIR, email_template_filename)
    return open(email_template_filepath).read()


def create_dir_for_file(file_path):
    dir_path = os.path.dirname(file_path)
    logging.info("Creating directory for path {}".format(dir_path))
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)
        logging.info("Created directory for path {}".format(dir_path))


def run_cmd(cmd):
    logging.info("Running subprocess: cmd({})".format(cmd))
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True)
    stdout, _ = process.communicate()
    return_code = process.returncode
    if return_code == 0:
        logging.info("Running subprocess completed: stdout({})".format(stdout))
        return stdout
    else:
        raise Exception("Running subprocess failed: return_code({}), stdout({})".format(return_code, stdout))


def run_stream_cmd(cmd):
    logging.info("Running subprocess: cmd({})".format(cmd))
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True)
    stdout, _ = process.communicate()
    return_code = process.returncode
    if return_code == 0:
        return stdout
    else:
        raise Exception("Running subprocess failed: return_code({}), stdout({})".format(return_code, stdout))


def run_stream_as_list_cmd(cmd):
    logging.info("Running subprocess: cmd({})".format(cmd))
    out = subprocess.check_output(cmd).split("\n")
    return out


def run_cmd_with_exception(cmd):
    logging.info("Running subprocess with handling exception : cmd({})".format(cmd))
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True)
    stdout, _ = process.communicate()
    return_code = process.returncode
    if return_code == 0:
        logging.info("Running subprocess completed: stdout({})".format(stdout))
        return stdout
    else:
        logging.error("Running subprocess failed: return_code({}), stdout({})".format(return_code, stdout))


def write_column_headers_to_file(file_path, content):
    cmd = "echo '{content}' | cat - '{file_path}' > '{temp_path}' && mv '{temp_path}' '{file_path}'".format(
        content=content,
        file_path=file_path,
        temp_path="{}.swp".format(file_path)
    )
    run_cmd(cmd)


def remove_file_pattern(file_path_pattern):
    cmd = "rm -rf {file_path_pattern}".format(file_path_pattern=file_path_pattern)
    run_cmd_with_exception(cmd)


def remove_file_pattern_using_airflow_user(file_path_pattern):
    cmd = "sudo -u {airflow_user} rm -rf {file_path_pattern}".format(airflow_user=HDFS_USER_ID, file_path_pattern=file_path_pattern)
    run_cmd_with_exception(cmd)


def touch_file_name(file_name):
    cmd = "touch {file_path_pattern}".format(file_path_pattern=file_name)
    run_cmd_with_exception(cmd)


def create_dirs_for_user(dir_path):
    cmd = "sudo -u {airflow_user} mkdir -p {dir_path}".format(airflow_user=HDFS_USER_ID, dir_path=dir_path)
    run_cmd(cmd)


def chmod_for_user(dir_path):
    cmd = "sudo -u {airflow_user} chmod -R 777 {dir_path}".format(airflow_user=HDFS_USER_ID, dir_path=dir_path)
    run_cmd_with_exception(cmd)


def line_count(file_path):
    cmd = "wc -l {file_path}".format(file_path=file_path)
    return int(run_cmd(cmd).decode().strip().split(' ')[0].strip())


def add_file_to_zip(input_file_path, zip_file_path):
    zip_file = ZipFile(zip_file_path, mode='w', compression=ZIP_DEFLATED, allowZip64=True)
    zip_file.write(input_file_path, os.path.basename(input_file_path))
    zip_file.close()


def determine_execution_date(**kwargs):
    if kwargs['execution_date'].second != 0:
        # Manual run
        execution_date = kwargs['prev_execution_date']
    else:
        # Scheduled or backfill run
        execution_date = kwargs['next_execution_date']

    return execution_date


def get_final_report_file_name(query_file_name, temp_hdfs_output_dir):
    return temp_hdfs_output_dir + "/final_" + query_file_name


def get_query_file_name(report_config, execution_date, start_date, end_date):
    return (report_config['name'] + "__" + get_formatted_date(execution_date) +
            "_" + get_formatted_date(start_date) + "_" + get_formatted_date(end_date) + "_query_output.csv")


def get_header_file_name(report_config, execution_date, start_date, end_date):
    return (report_config['name'] + "__" + get_formatted_date(execution_date) +
            "_" + get_formatted_date(start_date) + "_" + get_formatted_date(end_date) + "_report_headers.csv")


def get_execution_date_range(execution_date, report_config):
    days_before_curr_date = DATE_RANGE_CONFIG.get(report_config['name'], {}).get('DAYS_BEFORE_CURR_DATE', 1)
    start_date = execution_date.subtract(days=days_before_curr_date).end_of('day')

    duration_in_days = DATE_RANGE_CONFIG.get(report_config['name'], {}).get('DURATION_IN_DAYS', 1)
    end_date = start_date.add(days=duration_in_days).start_of('day')

    return start_date, end_date
