import csv
import json
import logging
import uuid
import traceback
import requests

from requests.adapters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Retry

from hive.hook.secured_hive_cli_hook import Secured<PERSON><PERSON><PERSON><PERSON><PERSON>ook

from mcp_reports.flox.config.config import HDFS_USER_ID, TEMP_TSV_HDFS_PATH, \
    REALM, HDFS_CLEANUP_PATH, FLOX_SERVICE_URL, OLYMPUS_PROTOCOL, OLYMPUS_HOST, OLYMPUS_PORT, \
    OLYMPUS_CLIENT_ID, OLYMPUS_CLIENT_KEY,RECON_ATTEMPT

from mcp_reports.flox.olympus_im_client import OlympusIMClient

from mcp_reports.flox.utils.common import read_query_template, format_query, \
    get_formatted_date, write_column_headers_to_file, apply_template, read_email_template, \
    remove_file_pattern, determine_execution_date, touch_file_name, \
    get_header_file_name, get_query_file_name, get_final_report_file_name, get_execution_date_range

from mcp_reports.flox.utils.hdfs import delete_from_hdfs, create_hdfs_dir, write_hive_hdfs_output_to_csv_file, \
    print_file_data, merge_in_hdfs_dir_from_header_and_query_data, print_file_data_final, \
    list_dir_files, get_hdfs_stream, concat_local_to_hdfs_dir

from mcp_reports.flox.zencast_client import ZencastClient

from datetime import datetime

RECON_PATH = "/v1/fulfilment/recon/"
ACCOUNTING_RECON_PATH = "/v1/accounting/recon/"
RECON = "RECON"
ACCOUNTING_RECON = "ACCOUNTING_RECON"

# method to create the HDFS file path for the report
def get_file_paths(report_name, execution_date, start_date, end_date, query):
    query = query.replace("/", "_") if query is not None else query
    # Remove .hql from query name
    query = query[:-4]
    temp_tsv_hdfs_path = TEMP_TSV_HDFS_PATH.format(REPORT_NAME=report_name,
                                                   EXECUTION_DATE=get_formatted_date(execution_date),
                                                   START_DATE=get_formatted_date(start_date),
                                                   END_DATE=get_formatted_date(end_date),
                                                   QUERY=query)

    temp_hdfs_output_dir = temp_tsv_hdfs_path[:-4]
    return temp_hdfs_output_dir


def execute_hive_query(report_config, **kwargs):

    # get execution details to create HDFS file path
    execution_date = determine_execution_date(**kwargs)
    start_date, end_date = get_execution_date_range(execution_date, report_config)
    
    for template_path in report_config['query']['templatePath']:
        query_template = read_query_template(template_path)
        temp_hdfs_output_dir = get_file_paths(report_config['name'], execution_date, start_date, end_date, template_path)

        # parse relevant fields to the query
        hql = format_query(query_template, execution_date, start_date, end_date)

        hive_config_default = {'hive.exec.compress.output': 'false', 'hive.merge.tezfiles': 'true',
                               'tez.queue.name': 'default'}

        hive_config = {}
        for key in hive_config_default.keys():
            hive_config[key] = hive_config_default[key]
        if 'hiveConfig' in report_config:
            for key in report_config['hiveConfig'].keys():
                hive_config[key] = report_config['hiveConfig'][key]
        config = []
        for key in hive_config.keys():
            config.append("""set {configKey} = {configValue};""".format(configKey=key, configValue=hive_config[key]))
        hive_config_str = '\n'.join(config)

        hql = """{hive_config_str}
        INSERT OVERWRITE DIRECTORY '{report_directory}' ROW FORMAT DELIMITED FIELDS TERMINATED BY ',' LINES TERMINATED BY '\n' NULL DEFINED AS ' ' STORED AS TEXTFILE
        """.format(hive_config_str=hive_config_str, report_directory=temp_hdfs_output_dir) + hql
        hive_hook = SecuredHiveCliHook(user=HDFS_USER_ID, realm=REALM)

        logging.info("Hql query ({})".format(hql))

        create_hdfs_dir(temp_hdfs_output_dir)
        hive_hook.run_cli(hql)


def generate_output_report(report_config, **kwargs):

    # get execution details
    execution_date = determine_execution_date(**kwargs)
    start_date, end_date = get_execution_date_range(execution_date, report_config)

    # join columns for header file
    columns = ",".join(report_config['columns'])
    if report_config['fileType'] == '.psv':
        columns = "|".join(report_config['columns'])

    for template_path in report_config['query']['templatePath']:
        # generate hdfs dir where query output from last operator can be found
        temp_hdfs_output_dir = get_hdfs_output_dir(report_config, execution_date, start_date, end_date, template_path)
        header_file_name = get_header_file_name(report_config, execution_date, start_date, end_date)
        query_file_name = get_query_file_name(report_config, execution_date, start_date, end_date)

        try:
            # from the output of the query executed, copy contents to a csv file
            write_hive_hdfs_output_to_csv_file(temp_hdfs_output_dir, query_file_name)
            # print_file_data(temp_hdfs_output_dir, query_file_name)
            touch_file_name(header_file_name)
            write_column_headers_to_file(header_file_name, columns)
            concat_local_to_hdfs_dir(temp_hdfs_output_dir, header_file_name)
            # print_file_data(temp_hdfs_output_dir, query_file_name)
            merge_in_hdfs_dir_from_header_and_query_data(temp_hdfs_output_dir, header_file_name, query_file_name)

            # create final report with headers and data
            print_file_data_final(temp_hdfs_output_dir, query_file_name)
            final_output_name_file = get_final_report_file_name(query_file_name, temp_hdfs_output_dir)

            # call flox for recon
            if report_config['name'] == 'FLOX_FULFILMENT_RECON':
                call_flox_for_recon(final_output_name_file, report_config, FLOX_SERVICE_URL + RECON_PATH, RECON)

            if report_config['name'] == 'FLOX_ACCOUNTING_RECON':
                call_flox_for_accounting_recon(final_output_name_file, report_config, FLOX_SERVICE_URL + ACCOUNTING_RECON_PATH, ACCOUNTING_RECON)

            list_dir_files(temp_hdfs_output_dir)
        except Exception:
            logging.error("Failed to generate output. Exception: %s" % traceback.format_exc())
        remove_file_pattern(header_file_name)


def call_flox_for_accounting_recon(final_output_name_file, report_config, flox_endpoint, recon_type):
    for _ in report_config['query']['templatePath']:
        logging.info("Opening file from the path for accounting recon ({})".format(final_output_name_file))

        # decode output stream and read
        encoding = 'ascii'
        stdout_bytes = get_hdfs_stream(final_output_name_file)
        output = stdout_bytes.decode(encoding)
        reader = csv.reader(output.splitlines(), delimiter=",")
        next(reader, None)
        lines = list(reader)
        logging.info("Number of rows found are: {}".format(len(lines)))

        s = requests.Session()
        retries = Retry(total=5,
                        backoff_factor=10,
                        status_forcelist=[400, 401, 402, 404, 500, 502, 503, 504],
                        method_whitelist=frozenset(['POST']))

        s.mount('https://', HTTPAdapter(max_retries=retries))

        for item in lines:
            item[0] = item[0] if item[0] != 'null' and item[0] != '\\N' else ""
            item[1] = item[1] if item[1] != 'null' and item[1] != '\\N' else ""
            item[2] = item[2] if item[2] != 'null' and item[2] != '\\N' else ""
            item[3] = item[3] if item[3] != 'null' and item[3] != '\\N' else ""
            reference_id = item[1]
            transaction_id = item[0]
            eventType = item[2]
            date = item[3]

            datetime_object = datetime.strptime(date.split('.')[0], '%Y-%m-%d %H:%M:%S')

            start_date = datetime.strptime('2025-01-30 22:00:01', '%Y-%m-%d %H:%M:%S')
            end_date = datetime.strptime('2025-01-31 14:59:59', '%Y-%m-%d %H:%M:%S')

            #Skipping the accounting events of this window because of the composit event flag issue
            if start_date <= datetime_object <= end_date:
                logging.info("Skipping accouting event for reference_id : " + reference_id)
                continue

            try:
                logging.info("Calling Flox for {}".format(recon_type))
                url = flox_endpoint + reference_id + "/" + transaction_id + "/" + eventType
                recon_schedule_request = {}
                headers = get_flox_service_headers()

                res = s.post(url, data=json.dumps(recon_schedule_request), headers=headers, verify=False)
                if res.status_code > 204:
                    logging.error("{} failed with error code ({})".format(recon_type, res.status_code))
                    raise Exception()
            except Exception as e:
                logging.error("{} failed with exception {}".format(recon_type, e))
                raise e

def call_flox_for_recon(final_output_name_file, report_config, flox_endpoint, recon_type):

    for _ in report_config['query']['templatePath']:
        logging.info("Opening file from the path for recon ({})".format(final_output_name_file))

        # decode output stream and read
        encoding = 'ascii'
        stdout_bytes = get_hdfs_stream(final_output_name_file)
        output = stdout_bytes.decode(encoding)
        reader = csv.reader(output.splitlines(), delimiter=",")
        next(reader, None)
        lines = list(reader)
        logging.info("Number of rows found are: {}".format(len(lines)))

        s = requests.Session()
        retries = Retry(total=5,
                        backoff_factor=10,
                        status_forcelist=[400, 401, 402, 404, 500, 502, 503, 504],
                        method_whitelist=frozenset(['POST']))

        s.mount('https://', HTTPAdapter(max_retries=retries))

        for item in lines:
            item[0] = item[0] if item[0] != 'null' and item[0] != '\\N' else ""
            group_fulfilment_id = item[0]
            try:
                logging.info("Calling Flox for group fulfilment {}".format(recon_type))
                url = flox_endpoint + group_fulfilment_id
                recon_schedule_request = {
                    "attempt": RECON_ATTEMPT
                }
                headers = get_flox_service_headers()

                res = s.post(url, data=json.dumps(recon_schedule_request), headers=headers, verify=False)
                if res.status_code > 204:
                    logging.error("Group fulfilment {} failed with error code ({})".format(recon_type, res.status_code))
                    raise Exception()
            except Exception as e:
                logging.error("Group fulfilment {} failed with exception {}".format(recon_type, e))
                raise e


def email_generated_report(report_config, **kwargs):
    if not report_config.get('email'):
        logging.info("Skipping email. No email config found.")
        return

    # get execution details
    email_config = report_config['email']
    execution_date = determine_execution_date(**kwargs)
    start_date, end_date = get_execution_date_range(execution_date, report_config)

    template_path = report_config['query']['templatePath'][0]
    temp_hdfs_output_dir = get_hdfs_output_dir(report_config, execution_date, start_date, end_date, template_path)
    query_file_name = get_query_file_name(report_config, execution_date, start_date, end_date)
    final_output_name_file = get_final_report_file_name(query_file_name, temp_hdfs_output_dir)

    logging.info("Opening file from the path ({})".format(final_output_name_file))

    # decode output stream to send as string in request to zencast
    encoding = 'ascii'
    stdout_bytes = get_hdfs_stream(final_output_name_file)
    output = stdout_bytes.decode(encoding)

    context = {
        'EXECUTION_DATE': get_formatted_date(execution_date),
        'START_DATE': get_formatted_date(start_date),
        'END_DATE': get_formatted_date(end_date)
    }
    request_id = str(uuid.uuid1())
    recipients = email_config['to']
    subject = email_config['subject'].format(**context)
    content = apply_template(read_email_template(
        email_config['templatePath']
    ).encode(), context)
    file_name = report_config['fileName'].format(**context)

    logging.info("Initializing Zencast client")
    zencast_client = ZencastClient()

    logging.info("Sending email to({}), subject({}) and requestId({}))".format(
            recipients[0], subject, request_id))
    try:
        zencast_client.send_email(request_id, str(recipients[0]), str(content), subject, file_name, output)
    except Exception:
        logging.error("Failed to send E-Mail. Exception: %s" % traceback.format_exc())


def clean_up_all_tmp_files(report_config, **kwargs):
    execution_date = determine_execution_date(**kwargs)
    start_date, end_date = get_execution_date_range(execution_date, report_config)

    for template_path in report_config['query']['templatePath']:
        temp_hdfs_output_dir = get_hdfs_output_dir(report_config, execution_date, start_date, end_date, template_path)
        logging.info("Cleaning csv hdfs path dir :({})".format(temp_hdfs_output_dir))
        delete_from_hdfs(temp_hdfs_output_dir)

    logging.info("Cleaning from all the tmp location is done for config {}".format(report_config))


def remove_hdfs_files(report_file_path):
    logging.info("Removing file from path of report {} ".format(report_file_path))
    delete_from_hdfs(report_file_path)


def remove_files(**kwargs):
    remove_hdfs_files(HDFS_CLEANUP_PATH)


def read_local_file(file_path):
    with open(file_path, "rb") as fil:
        return fil.read()


def get_hdfs_output_dir(report_config, execution_date, start_date, end_date, template_path):
    temp_hdfs_output_dir = get_file_paths(report_config['name'], execution_date, start_date, end_date, template_path)
    return temp_hdfs_output_dir


def get_flox_service_headers():
    olympus_client = OlympusIMClient(OLYMPUS_PROTOCOL, OLYMPUS_HOST, OLYMPUS_PORT,
                                     OLYMPUS_CLIENT_ID, OLYMPUS_CLIENT_KEY)
    return {"Content-Type": "application/json", "Accept": "application/json",
            'Authorization': olympus_client.get_bearer_auth_token()}
