import os
from datetime import timedelta

import pendulum
from airflow import DAG

from mcp_reports.flox.config.config import REPORT_CONFIG_DIR, REPORT_CLEANUP_CONFIG_DIR
from mcp_reports.flox.operators import execute_hive_query_operator, generate_output_report_operator, \
    email_generated_report_operator, delete_tenant_file_operator, clean_up_all_tmp_files_operator
from mcp_reports.flox.utils.common import read_report_config

AIRFLOW_DEFAULT_ARGS = {
    'owner': 'phonepe_mcp_airflow_user',
    'depends_on_past': False,
    'email': ['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': True,
    'retries': 0,
    'retry_delay': timedelta(minutes=10),
    'start_date': pendulum.datetime(2022, 11, 22)
}


def generate_dag(dag_id, config):
    return DAG(
        dag_id,
        default_args=AIRFLOW_DEFAULT_ARGS,
        description=config.get('description', config['name']),
        schedule_interval=config['schedule'],
        catchup=False,
    )


def configure_dag(dag, report_config):
    query_source = report_config['query']['source']
    if query_source == 'hive':
        execute_query_op = execute_hive_query_operator(dag, report_config)
    else:
        raise Exception('Invalid query source supplied')

    generate_report_op = generate_output_report_operator(dag, report_config)
    clean_up_all_tmp_files_op = clean_up_all_tmp_files_operator(dag, report_config)

    if report_config.get('email'):
        email_report_op = email_generated_report_operator(dag, report_config)
        execute_query_op >> generate_report_op >> email_report_op >> clean_up_all_tmp_files_op
    else:
        execute_query_op >> generate_report_op >> clean_up_all_tmp_files_op


def configure_cleanup_dag(dag,report_cleanup_config):
    delete_tenant_file_operator(dag, report_cleanup_config)


def setup_cleanup_tenant(report_cleanup_configs):
    for report_cleanup_config in report_cleanup_configs:
        dag_id = '{}_v{}'.format(report_cleanup_config['name'], report_cleanup_config.get('version', 1))
        dag = generate_dag(dag_id, report_cleanup_config)
        configure_cleanup_dag(dag, report_cleanup_config)
        globals()[dag_id] = dag


def main():
    report_config_files = [os.path.join(dp, f) for dp, dn, filenames in os.walk(REPORT_CONFIG_DIR) for f in filenames]
    report_configs = [read_report_config(rcf) for rcf in report_config_files]

    for report_config in report_configs:
        dag_id = '{}_v{}'.format(report_config['name'], report_config.get('version', 1))
        dag = generate_dag(dag_id, report_config)
        configure_dag(dag, report_config)
        globals()[dag_id] = dag

    report_cleanup_config_files = [os.path.join(dp, f) for dp, dn, filenames in os.walk(REPORT_CLEANUP_CONFIG_DIR) for f in filenames]
    report_cleanup_configs = [read_report_config(scf) for scf in report_cleanup_config_files]

    setup_cleanup_tenant(report_cleanup_configs)


main()
