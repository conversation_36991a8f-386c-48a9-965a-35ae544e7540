import csv
import logging

def process(tsv_file_path, csv_file_path, execution_date):
    with open(csv_file_path, 'w') as csvfile:
        logging.info("generating match report...")
        writer = csv.writer(csvfile, delimiter=',', quotechar='|', quoting=csv.QUOTE_MINIMAL)
        tsv_file = open(tsv_file_path)
        rows = list(csv.reader(tsv_file, delimiter='\t'))
        logging.info("Number of rows found are {}".format(len(rows)))
        for row in rows:
            row[0] = row[0] if row[0] != 'null' and row[0] != '\\N'else ""
            row[1] = row[1] if row[1] != 'null' and row[1] != '\\N'else ""
            row[2] = row[2] if row[2] != 'null' and row[2] != '\\N'else ""

            writer.writerow(row)
            
       