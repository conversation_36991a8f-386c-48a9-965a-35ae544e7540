import csv
import logging
from mcp_reports.torch.utils.common import days_between

def process(tsv_file_path, csv_file_path, execution_date):
    with open(csv_file_path, 'w') as csvfile:
        logging.info("generating mismatch report...")
        writer = csv.writer(csvfile, delimiter=',', quotechar='|', quoting=csv.QUOTE_MINIMAL)
        tsv_file = open(tsv_file_path)
        rows = list(csv.reader(tsv_file, delimiter='\t'))
        logging.info("Number of rows found are {}".format(len(rows)))
        for row in rows:
            for i in range(len(row)):
                if row[i] == '\N':
                    row[i] = ''
            row.extend(["","","","",""])
            ageing_days = days_between(row[6], execution_date)
            if ageing_days > 30:
                row[-1] = 'X'
            elif ageing_days > 20:
                row[-2] = 'X'
            elif ageing_days > 10:
                row[-3] = 'X'
            elif ageing_days > 5:
                row[-4] = 'X'
            elif ageing_days > 0:
                row[-5] = 'X'
            writer.writerow(row)

