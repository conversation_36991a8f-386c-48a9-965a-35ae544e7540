import csv
import logging

def process(tsv_file_path, csv_file_path, execution_date):
    with open(csv_file_path, 'a') as csvfile:
        logging.info("generating summary report...")
        writer = csv.writer(csvfile, delimiter=',', quotechar='|', quoting=csv.QUOTE_MINIMAL)
        tsv_file = open(tsv_file_path)
        rows = list(csv.reader(tsv_file, delimiter='\t'))
        logging.info("Number of rows found are {}".format(len(rows)))
        for row in rows:
            row = row[:-1]
            if row[5] == '0':
                row[5] = 'Redemption REVERSAL'
            if row[5] == '1':
                row[5] = 'Redemption'
            for i in range(len(row)):
                if row[i] == '\N':
                    row[i] = ''
            writer.writerow(row)