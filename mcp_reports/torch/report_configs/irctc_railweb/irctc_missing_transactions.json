{"name": "TORCH_IRCTCRAILWEB_MISSING_TRANSACTIONS", "fileName": "MISSING_TRANSACTIONS_FOR_{MERCHANT_ID}_{START_DATE}_{END_DATE}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["irctc/torch_missing_transactions.hql"]}, "schedule": "0 10 * * *", "merchantId": "IRCTCRAILWEB", "pythonFile": "torch_missing_transactions.py", "columns": ["Transaction ID", "External Transaction ID", "Original Transaction ID", "Transaction Type", "Reason", "PP Value", "{MERCHANT_ID} Value", "Txn date", "0-5 days", "6-10 days", "11-20 days", "21-30 days", "> 30 days"], "email": {"condition": {">": [{"var": "LINE_COUNT"}, 1]}, "to": ["<EMAIL>"], "subject": "Bucketing of missing transactions for {MERCHANT_ID} from {START_DATE} to {END_DATE}", "templatePath": "torch_missing_transactions.html"}}