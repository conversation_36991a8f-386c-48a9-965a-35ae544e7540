{"name": "TORCH_AIRTELDTHDIRECT_MISSING_TRANSACTIONS_DAILY", "fileName": "MISSING_TRANSACTIONS_FOR_{MERCHANT_ID}_{START_DATE}_{END_DATE}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["airtel_dth/torch_missing_transactions.hql"]}, "schedule": "0 17 * * *", "merchantId": "AIRTELDTHDIRECT", "pythonFile": "torch_missing_transactions.py", "columns": ["Nexus Id", "MIS External Transaction ID", "Settlement External Transaction ID", "Transaction Type", "Reason", "PP Value", "{MERCHANT_ID} Value", "MIS Txn date", "Settlement Txn date", "0-5 days", "6-10 days", "11-20 days", "21-30 days", "> 30 days"], "email": {"condition": {">": [{"var": "LINE_COUNT"}, 1]}, "to": ["<EMAIL>", "<EMAIL>"], "subject": "Bucketing of missing transactions for {MERCHANT_ID} from {START_DATE} to {END_DATE}", "templatePath": "torch_missing_transactions.html"}}