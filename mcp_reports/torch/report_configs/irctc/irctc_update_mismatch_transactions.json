{"name": "TORCH_IRCTC_UPDATE_PREVIOUSLY_MISMATCHED_TRANSACTIONS", "fileName": "MISMATCHED_TRANSACTIONS_FOR_{MERCHANT_ID}_{START_DATE}_{END_DATE}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["irctc/torch_match_transactions.hql"]}, "schedule": "0 8 * * *", "merchantId": "IRCTC", "pythonFile": "torch_match_transactions.py", "columns": ["Transaction Id", "External Transaction Id", "Original Transaction Id", "Transaction Type", "Transaction Date", "Amount"], "email": {"condition": {">": [{"var": "LINE_COUNT"}, 1]}, "to": ["<EMAIL>"], "subject": "Updating status of mismatched transactions for {MERCHANT_ID} from {START_DATE} to {END_DATE} which got matched", "templatePath": "torch_update_mismatch_transactions.html"}}