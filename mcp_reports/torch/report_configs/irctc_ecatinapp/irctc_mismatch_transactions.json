{"name": "TORCH_IRCTCECATINAPP_MISMATCH_TRANSACTIONS", "fileName": "MISMATCH_TRANSACTIONS_FOR_{MERCHANT_ID}_{START_DATE}_{END_DATE}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["irctc_tourism/torch_mismatch_transactions.hql"]}, "schedule": "0 10 * * *", "merchantId": "IRCTCECATINAPP", "pythonFile": "torch_mismatch_transactions.py", "columns": ["Transaction ID", "External Transaction ID", "Original Transaction ID", "Transaction Type", "PP Value", "{MERCHANT_ID} Value", "Txn date", "0-5 days", "6-10 days", "11-20 days", "21-30 days", "> 30 days"], "email": {"condition": {">": [{"var": "LINE_COUNT"}, 1]}, "to": ["<EMAIL>"], "subject": "Bucketing of mismatch transactions for {MERCHANT_ID} from {START_DATE} to {END_DATE}", "templatePath": "torch_mismatch_transactions.html"}}