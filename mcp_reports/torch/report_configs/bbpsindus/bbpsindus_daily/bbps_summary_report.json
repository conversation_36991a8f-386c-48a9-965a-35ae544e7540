{"name": "TORCH_BBPSINDUS_SUMMARY_REPORT_DAILY", "fileName": "SETTLEMENT_SUMMARY_{START_DATE}_{END_DATE}_FOR_{MERCHANT_ID}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["bbpsindus/torch_forward.hql", "bbpsindus/torch_reverse.hql"]}, "schedule": "0 22 * * *", "merchantId": "BBPSINDUS", "pythonFile": "torch_summary_report.py", "columns": ["Settlement Date", "Transaction Date", "Merchant ID", "Transaction Type", "Count of Txn", "Total Amount", "Fee", "IGST", "CGST", "SGST", "Net Amount", "Amount as per {MERCHANT_ID}", "Diff", "Reasons"], "email": {"to": ["<EMAIL>", "<EMAIL>"], "subject": "Torch {MERCHANT_ID} summary report b/w {START_DATE} to {END_DATE}", "templatePath": "summary_report.html"}}