{"name": "TORCH_BBPSINDUS_UPDATE_MATCH_TRANSACTIONS", "fileName": "MATCHED_TRANSACTIONS_FOR_{MERCHANT_ID}_{START_DATE}_{END_DATE}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["bbpsindus/torch_match_transactions.hql"]}, "schedule": "0 1 4 * *", "merchantId": "BBPSINDUS", "pythonFile": "torch_match_transactions.py", "columns": ["PP reference ID", "External Transaction ID", "Original Transaction ID", "Transaction Type", "Transaction Date", "Amount"], "email": {"condition": {">": [{"var": "LINE_COUNT"}, 1]}, "to": ["<EMAIL>", "<EMAIL>"], "subject": "Updating status of matched transactions for {MERCHANT_ID} from {START_DATE} to {END_DATE}", "templatePath": "torch_match_transactions.html"}}