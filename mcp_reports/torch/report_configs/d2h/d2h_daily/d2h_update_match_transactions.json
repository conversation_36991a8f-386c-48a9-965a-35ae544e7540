{"name": "TORCH_D2HEPRS_UPDATE_MATCH_TRANSACTIONS_DAILY", "fileName": "MATCHED_TRANSACTIONS_FOR_{MERCHANT_ID}_{START_DATE}_{END_DATE}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["d2h_dish/torch_match_transactions.hql"]}, "schedule": "0 22 * * *", "merchantId": "D2HEPRS", "pythonFile": "torch_match_transactions.py", "columns": ["Nexus Id", "Transaction Id", "External Transaction Id", "Original Transaction Id", "Transaction Type", "Transaction Date", "Settlement Date", "MIS Amount", "Settlement Amount"], "email": {"condition": {">": [{"var": "LINE_COUNT"}, 1]}, "to": ["<EMAIL>", "<EMAIL>"], "subject": "Updating status of matched transactions for {MERCHANT_ID} from {START_DATE} to {END_DATE}", "templatePath": "torch_match_transactions.html"}}