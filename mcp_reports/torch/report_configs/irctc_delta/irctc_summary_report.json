{"name": "TORCH_IRCTC_DELTA_SUMMARY_REPORT", "fileName": "SETTLEMENT_SUMMARY_{START_DATE}_{END_DATE}_FOR_{MERCHANT_ID}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["irctc_delta/torch_forward.hql", "irctc/torch_reverse.hql"]}, "schedule": "0 6 * * *", "merchantId": "IRCTC", "pythonFile": "torch_summary_report.py", "columns": ["File Upload Date", "Creation Date", "Transaction Date", "{MERCHANT_ID} RDS Date", "Merchant ID", "Transaction Type", "Count of Txn", "Total Amount", "Fee", "IGST", "CGST", "SGST", "Net Amount", "Amount as per {MERCHANT_ID}", "Diff", "Reasons"], "email": {"to": ["<EMAIL>"], "subject": "Torch {MERCHANT_ID} summary report b/w {START_DATE} to {END_DATE}", "templatePath": "summary_report.html"}}