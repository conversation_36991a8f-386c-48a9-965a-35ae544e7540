{"name": "TORCH_BBPSBP_MISMATCH_TRANSACTIONS", "fileName": "MISMATCH_TRANSACTIONS_FOR_{MERCHANT_ID}_{START_DATE}_{END_DATE}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["bbps/torch_mismatch_transactions.hql"]}, "schedule": "0 1 4 * *", "merchantId": "BBPSBP", "pythonFile": "torch_mismatch_transactions.py", "columns": ["Nexus Id", "Transaction ID", "External Transaction ID", "Transaction Type", "Txn date", "Settlement Date", "PP Value", "{MERCHANT_ID} Value"], "email": {"condition": {">": [{"var": "LINE_COUNT"}, 1]}, "to": ["<EMAIL>", "<EMAIL>"], "subject": "Bucketing of mismatch transactions for {MERCHANT_ID} from {START_DATE} to {END_DATE}", "templatePath": "torch_mismatch_transactions.html"}}