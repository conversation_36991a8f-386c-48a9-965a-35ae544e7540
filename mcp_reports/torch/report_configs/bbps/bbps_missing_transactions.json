{"name": "TORCH_BBPSBP_MISSING_TRANSACTIONS", "fileName": "MISSING_TRANSACTIONS_FOR_{MERCHANT_ID}_{START_DATE}_{END_DATE}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["bbps/torch_missing_transactions.hql"]}, "schedule": "0 1 4 * *", "merchantId": "BBPSBP", "pythonFile": "torch_missing_transactions.py", "columns": ["PP reference ID", "External Transaction ID", "Original Transaction ID", "Transaction Type", "Reason", "PP Value", "{MERCHANT_ID} Value", "Txn date", "PP Settlement Date"], "email": {"condition": {">": [{"var": "LINE_COUNT"}, 1]}, "to": ["<EMAIL>", "<EMAIL>"], "subject": "Bucketing of missing transactions for {MERCHANT_ID} from {START_DATE} to {END_DATE}", "templatePath": "torch_missing_transactions.html"}}