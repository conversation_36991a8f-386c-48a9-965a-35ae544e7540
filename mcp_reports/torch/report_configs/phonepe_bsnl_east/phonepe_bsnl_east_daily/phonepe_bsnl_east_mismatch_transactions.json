{"name": "TORCH_PHONEPEBSNLEAST_MISMATCH_TRANSACTIONS_DAILY", "fileName": "MISMATCH_TRANSACTIONS_FOR_{MERCHANT_ID}_{START_DATE}_{END_DATE}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["bsnl_direct/torch_mismatch_transactions.hql"]}, "schedule": "0 0 * * *", "merchantId": "PHONEPEBSNLEAST", "pythonFile": "torch_mismatch_transactions.py", "columns": ["Nexus Id", "Transaction ID", "External Transaction ID", "Transaction Type", "PP Value", "{MERCHANT_ID} Value", "Txn date", "Settlement Date", "0-5 days", "6-10 days", "11-20 days", "21-30 days", "> 30 days"], "email": {"condition": {">": [{"var": "LINE_COUNT"}, 1]}, "to": ["<EMAIL>", "<EMAIL>"], "subject": "Bucketing of mismatch transactions for {MERCHANT_ID} from {START_DATE} to {END_DATE}", "templatePath": "torch_mismatch_transactions.html"}}