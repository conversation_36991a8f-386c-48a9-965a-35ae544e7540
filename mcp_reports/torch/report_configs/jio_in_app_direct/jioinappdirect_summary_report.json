{"name": "TORCH_JIOINAPPDIRECT_SUMMARY_REPORT", "fileName": "SETTLEMENT_SUMMARY_{START_DATE}_{END_DATE}_FOR_{MERCHANT_ID}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["jio_in_app_direct/torch_forward.hql"]}, "schedule": "0 1 5 * *", "merchantId": "JIOINAPPDIRECT", "pythonFile": "torch_summary_report.py", "columns": ["Settlement Date", "Transaction Date", "Merchant ID", "Nexus MID", "Transaction Type", "Count of Txn", "Total Amount", "Fee", "IGST", "CGST", "SGST", "Net Amount", "Amount as per {MERCHANT_ID}", "Diff", "Reasons"], "email": {"to": ["<EMAIL>", "<EMAIL>"], "subject": "Torch {MERCHANT_ID} summary report b/w {START_DATE} to {END_DATE}", "templatePath": "summary_report.html"}}