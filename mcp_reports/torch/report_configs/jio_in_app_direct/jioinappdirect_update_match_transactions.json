{"name": "TORCH_JIOINAPPDIRECT_UPDATE_MATCH_TRANSACTIONS", "fileName": "MATCHED_TRANSACTIONS_FOR_{MERCHANT_ID}_{START_DATE}_{END_DATE}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["jio_in_app_direct/torch_match_transactions.hql"]}, "schedule": "0 1 5 * *", "merchantId": "JIOINAPPDIRECT", "pythonFile": "torch_match_transactions.py", "columns": ["Nexus Id", "Transaction Id", "External Transaction Id", "Original Transaction Id", "Transaction Type", "Transaction Date", "MIS Amount", "Settlement Amount", "Nexus MID"], "email": {"condition": {">": [{"var": "LINE_COUNT"}, 1]}, "to": ["<EMAIL>", "<EMAIL>"], "subject": "Updating status of matched transactions for {MERCHANT_ID} from {START_DATE} to {END_DATE}", "templatePath": "torch_match_transactions.html"}}