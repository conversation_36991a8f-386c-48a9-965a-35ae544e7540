{"name": "TORCH_TATAPLAYDIRECT_REVERSE_UPDATE_MATCH_TRANSACTIONS", "fileName": "MATCHED_TRANSACTIONS_FOR_{MERCHANT_ID}_{START_DATE}_{END_DATE}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["d2h_dish/torch_match_transactions.hql"]}, "schedule": "0 6 * * *", "merchantId": "TATAPLAYDIRECT", "transactionType": [1, 2], "pythonFile": "torch_match_transactions.py", "columns": ["Transaction Id", "External Transaction Id", "Original Transaction Id", "Transaction Type", "Transaction Date", "Amount"], "email": {"condition": {">": [{"var": "LINE_COUNT"}, 1]}, "to": ["<EMAIL>"], "subject": "Updating status of matched transactions for {MERCHANT_ID} from {START_DATE} to {END_DATE}", "templatePath": "torch_match_transactions.html"}}