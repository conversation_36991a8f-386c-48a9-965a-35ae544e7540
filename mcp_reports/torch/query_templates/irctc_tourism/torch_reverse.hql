SELECT  DATE(merchant_settlement_data.created) as file_upload_date,
        DATE(settlement_entries_data.created) as created_date,
        DATE(settlement_entries_data.transaction_date) as transaction_date,
        DATE(merchant_settlement_data.settlement_date) as settlement_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "REVERSE_REVENUE",
        count(merchant_settlement_data.original_transaction_id) as count_of_txn,
        sum(settlement_entries_data.total_receivable - settlement_entries_data.total_payable) as total_amount,
        sum(-1*settlement_entries_data.fee) as fee,
        sum(-1*settlement_entries_data.igst) as igst,
        sum(-1*settlement_entries_data.cgst) as cgst,
        sum(-1*settlement_entries_data.sgst) as sgst,
        sum(settlement_entries_data.amount) as net_amount,
        sum(merchant_settlement_data.transaction_amount) as amount_as_per_irctc,
        sum(settlement_entries_data.amount - merchant_settlement_data.transaction_amount) as diff,
        "Matching with {{MERCHANT_ID}}" as Reasons,
        1 as filter

FROM (select merchant_settlement_record.original_transaction_id,  sum(merchant_settlement_record.transaction_amount) as transaction_amount, max(merchant_settlement.created) as created, max(merchant_settlement.settlement_date) as settlement_date from torch.merchant_settlement_record as merchant_settlement_record
INNER JOIN torch.merchant_settlement as merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
where merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND merchant_settlement_record.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND tenant.provider_name = '{{MERCHANT_ID}}'
group by original_transaction_id) merchant_settlement_data
INNER JOIN (select ledger_entries.original_transaction_id, max(settlement_entries.created) as created,
max(settlement_entries.transaction_date) as transaction_date, sum(settlement_entries.total_receivable) as total_receivable,
sum(settlement_entries.total_payable) as total_payable, sum(settlement_entries.fee) as fee, sum(settlement_entries.igst) as igst,
sum(settlement_entries.sgst) as sgst, sum(settlement_entries.cgst) as cgst, sum(settlement_entries.amount) as amount from accounting.ledger_entries AS ledger_entries
INNER JOIN accounting.settlement_entries AS settlement_entries on ledger_entries.settlement_entry_id = settlement_entries.id
left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
on settlement_entries.from_party_id = q2.id
where (q1.id is not null OR q2.id is not null)
AND ledger_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND ledger_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND ledger_entries.event_type = 'MERCHANT_FULFILMENT_REVERSAL'
AND settlement_entries.event_type = 'MERCHANT_FULFILMENT_REVERSAL'
AND settlement_entries.transaction_type = 'REVENUE'
AND settlement_entries.entry_type = 1
group by original_transaction_id ) settlement_entries_data
on merchant_settlement_data.original_transaction_id = settlement_entries_data.original_transaction_id
where ABS(merchant_settlement_data.transaction_amount - settlement_entries_data.amount) < 1
group by DATE(merchant_settlement_data.created), DATE(settlement_entries_data.created),  DATE(settlement_entries_data.transaction_date), DATE(merchant_settlement_data.settlement_date)

UNION

SELECT  DATE(merchant_settlement_data.created) as file_upload_date,
        DATE(settlement_entries_data.created) as created_date,
        DATE(settlement_entries_data.transaction_date) as transaction_date,
        DATE(merchant_settlement_data.settlement_date) as settlement_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "REVERSE_REVENUE",
        count(merchant_settlement_data.original_transaction_id) as count_of_txn,
        sum(settlement_entries_data.total_receivable - settlement_entries_data.total_payable) as total_amount,
        sum(-1*settlement_entries_data.fee) as fee,
        sum(-1*settlement_entries_data.igst) as igst,
        sum(-1*settlement_entries_data.cgst) as cgst,
        sum(-1*settlement_entries_data.sgst) as sgst,
        sum(settlement_entries_data.amount) as net_amount,
        sum(merchant_settlement_data.transaction_amount) as amount_as_per_irctc,
        sum(settlement_entries_data.amount - merchant_settlement_data.transaction_amount) as diff,
        "Mismatch with {{MERCHANT_ID}}" as Reasons,
        2 as filter

FROM (select merchant_settlement_record.original_transaction_id,  sum(merchant_settlement_record.transaction_amount) as transaction_amount, max(merchant_settlement.created) as created, max(merchant_settlement.settlement_date) as settlement_date from torch.merchant_settlement_record as merchant_settlement_record
INNER JOIN torch.merchant_settlement as merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
where merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND merchant_settlement_record.transaction_date < DATE_ADD('{{END_DATE}}', {{BEFORE_DAY_RANGE}})
AND tenant.provider_name = '{{MERCHANT_ID}}'
group by original_transaction_id) merchant_settlement_data
INNER JOIN (select ledger_entries.original_transaction_id, max(settlement_entries.created) as created,
max(settlement_entries.transaction_date) as transaction_date, sum(settlement_entries.total_receivable) as total_receivable,
sum(settlement_entries.total_payable) as total_payable, sum(settlement_entries.fee) as fee, sum(settlement_entries.igst) as igst,
sum(settlement_entries.sgst) as sgst, sum(settlement_entries.cgst) as cgst, sum(settlement_entries.amount) as amount from accounting.ledger_entries AS ledger_entries
INNER JOIN accounting.settlement_entries AS settlement_entries on ledger_entries.settlement_entry_id = settlement_entries.id
left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
on settlement_entries.from_party_id = q2.id
where (q1.id is not null OR q2.id is not null)
AND ledger_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND ledger_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND ledger_entries.event_type = 'MERCHANT_FULFILMENT_REVERSAL'
AND settlement_entries.event_type = 'MERCHANT_FULFILMENT_REVERSAL'
AND settlement_entries.transaction_type = 'REVENUE'
AND settlement_entries.entry_type = 1
group by original_transaction_id ) settlement_entries_data
on merchant_settlement_data.original_transaction_id = settlement_entries_data.original_transaction_id
where ABS(merchant_settlement_data.transaction_amount - settlement_entries_data.amount) > 1
group by DATE(merchant_settlement_data.created), DATE(settlement_entries_data.created),  DATE(settlement_entries_data.transaction_date), DATE(merchant_settlement_data.settlement_date)

UNION

SELECT  DATE("") as file_upload_date,
        DATE(settlement_entries_data.created) as created_date,
        DATE(settlement_entries_data.transaction_date) as transaction_date,
        DATE("") as settlement_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "REVERSE_REVENUE",
        count(settlement_entries_data.original_transaction_id) as count_of_txn,
        sum(settlement_entries_data.total_receivable - settlement_entries_data.total_payable) as total_amount,
        sum(-1*settlement_entries_data.fee) as fee,
        sum(-1*settlement_entries_data.igst) as igst,
        sum(-1*settlement_entries_data.cgst) as cgst,
        sum(-1*settlement_entries_data.sgst) as sgst,
        sum(settlement_entries_data.amount) as net_amount,
        0 as amount_as_per_irctc,
        sum(settlement_entries_data.amount) as diff,
        "Missing in MIS report" as Reasons,
        3 as filter

FROM (select merchant_settlement_record.original_transaction_id,  sum(merchant_settlement_record.transaction_amount) as transaction_amount, max(merchant_settlement.created) as created, max(merchant_settlement.settlement_date) as settlement_date from torch.merchant_settlement_record as merchant_settlement_record
INNER JOIN torch.merchant_settlement as merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
where merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND merchant_settlement_record.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND tenant.provider_name = '{{MERCHANT_ID}}'
group by original_transaction_id) merchant_settlement_data
RIGHT JOIN (select ledger_entries.original_transaction_id, max(settlement_entries.created) as created,
max(settlement_entries.transaction_date) as transaction_date, sum(settlement_entries.total_receivable) as total_receivable,
sum(settlement_entries.total_payable) as total_payable, sum(settlement_entries.fee) as fee, sum(settlement_entries.igst) as igst,
sum(settlement_entries.sgst) as sgst, sum(settlement_entries.cgst) as cgst, sum(settlement_entries.amount) as amount from accounting.ledger_entries AS ledger_entries
INNER JOIN accounting.settlement_entries AS settlement_entries on ledger_entries.settlement_entry_id = settlement_entries.id
left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
on settlement_entries.from_party_id = q2.id
where (q1.id is not null OR q2.id is not null)
AND ledger_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND ledger_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND ledger_entries.event_type = 'MERCHANT_FULFILMENT_REVERSAL'
AND settlement_entries.event_type = 'MERCHANT_FULFILMENT_REVERSAL'
AND settlement_entries.transaction_type = 'REVENUE'
AND settlement_entries.entry_type = 1
group by original_transaction_id ) settlement_entries_data
on merchant_settlement_data.original_transaction_id = settlement_entries_data.original_transaction_id
where merchant_settlement_data.original_transaction_id IS NULL
group by DATE(settlement_entries_data.created),  DATE(settlement_entries_data.transaction_date)


UNION

SELECT  DATE(merchant_settlement_data.created) as file_upload_date,
        DATE("") as created_date,
        DATE("") as transaction_date,
        DATE(merchant_settlement_data.settlement_date) as settlement_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "REVERSE_REVENUE",
        count(merchant_settlement_data.original_transaction_id) as count_of_txn,
        0 as total_amount,
        0 as fee,
        0 as igst,
        0 as cgst,
        0 as sgst,
        0 as net_amount,
        sum(merchant_settlement_data.transaction_amount) as amount_as_per_irctc,
        sum(-1*merchant_settlement_data.transaction_amount) as diff,
        "Missing in Accounting" as Reasons,
        4 as filter


FROM (select merchant_settlement_record.original_transaction_id,  sum(merchant_settlement_record.transaction_amount) as transaction_amount, max(merchant_settlement.created) as created, max(merchant_settlement.settlement_date) as settlement_date from torch.merchant_settlement_record as merchant_settlement_record
INNER JOIN torch.merchant_settlement as merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
where merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND merchant_settlement_record.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND tenant.provider_name = '{{MERCHANT_ID}}'
group by original_transaction_id) merchant_settlement_data
LEFT JOIN (select ledger_entries.original_transaction_id, max(settlement_entries.created) as created,
max(settlement_entries.transaction_date) as transaction_date, sum(settlement_entries.total_receivable) as total_receivable,
sum(settlement_entries.total_payable) as total_payable, sum(settlement_entries.fee) as fee, sum(settlement_entries.igst) as igst,
sum(settlement_entries.sgst) as sgst, sum(settlement_entries.cgst) as cgst, sum(settlement_entries.amount) as amount from accounting.ledger_entries AS ledger_entries
INNER JOIN accounting.settlement_entries AS settlement_entries on ledger_entries.settlement_entry_id = settlement_entries.id
left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
on settlement_entries.from_party_id = q2.id
where (q1.id is not null OR q2.id is not null)
AND ledger_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND ledger_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND ledger_entries.event_type = 'MERCHANT_FULFILMENT_REVERSAL'
AND settlement_entries.event_type = 'MERCHANT_FULFILMENT_REVERSAL'
AND settlement_entries.transaction_type = 'REVENUE'
AND settlement_entries.entry_type = 1
group by original_transaction_id ) settlement_entries_data
on merchant_settlement_data.original_transaction_id = settlement_entries_data.original_transaction_id
where settlement_entries_data.original_transaction_id is NULL
group by DATE(merchant_settlement_data.created), DATE(merchant_settlement_data.settlement_date)


UNION

SELECT  DATE(merchant_settlement_data.created) as file_upload_date,
        DATE(settlement_entries_data.created) as created_date,
        DATE(settlement_entries_data.transaction_date) as transaction_date,
        DATE(merchant_settlement_data.settlement_date) as settlement_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "REVERSE_REDEMPTION",
        count(merchant_settlement_data.original_transaction_id) as count_of_txn,
        sum(settlement_entries_data.total_receivable - settlement_entries_data.total_payable) as total_amount,
        sum(-1*settlement_entries_data.fee) as fee,
        sum(-1*settlement_entries_data.igst) as igst,
        sum(-1*settlement_entries_data.cgst) as cgst,
        sum(-1*settlement_entries_data.sgst) as sgst,
        sum(settlement_entries_data.amount) as net_amount,
        sum(merchant_settlement_data.transaction_amount) as amount_as_per_irctc,
        sum(settlement_entries_data.amount - merchant_settlement_data.transaction_amount) as diff,
        "Matching with {{MERCHANT_ID}}" as Reasons,
        1 as filter

FROM (select merchant_settlement_record.original_transaction_id,  sum(merchant_settlement_record.transaction_amount) as transaction_amount, max(merchant_settlement.created) as created, max(merchant_settlement.settlement_date) as settlement_date from torch.merchant_settlement_record as merchant_settlement_record
INNER JOIN torch.merchant_settlement as merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
where merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND merchant_settlement_record.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND tenant.provider_name = '{{MERCHANT_ID}}'
group by original_transaction_id) merchant_settlement_data
INNER JOIN (select ledger_entries.original_transaction_id, max(settlement_entries.created) as created,
max(settlement_entries.transaction_date) as transaction_date, sum(settlement_entries.total_receivable) as total_receivable,
sum(settlement_entries.total_payable) as total_payable, sum(settlement_entries.fee) as fee, sum(settlement_entries.igst) as igst,
sum(settlement_entries.sgst) as sgst, sum(settlement_entries.cgst) as cgst, sum(settlement_entries.amount) as amount from accounting.ledger_entries AS ledger_entries
INNER JOIN accounting.settlement_entries AS settlement_entries on ledger_entries.settlement_entry_id = settlement_entries.id
left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
on settlement_entries.from_party_id = q2.id
where (q1.id is not null OR q2.id is not null)
AND ledger_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND ledger_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND ledger_entries.event_type = 'MERCHANT_FULFILMENT_REVERSAL'
AND settlement_entries.event_type = 'MERCHANT_FULFILMENT_REVERSAL'
AND settlement_entries.transaction_type = 'REDEMPTION_REVERSE'
AND settlement_entries.entry_type = 0
group by original_transaction_id ) settlement_entries_data
on merchant_settlement_data.original_transaction_id = settlement_entries_data.original_transaction_id
where ABS(merchant_settlement_data.transaction_amount - settlement_entries_data.amount) < 1
group by DATE(merchant_settlement_data.created), DATE(settlement_entries_data.created),  DATE(settlement_entries_data.transaction_date), DATE(merchant_settlement_data.settlement_date)

UNION

SELECT  DATE(merchant_settlement_data.created) as file_upload_date,
        DATE(settlement_entries_data.created) as created_date,
        DATE(settlement_entries_data.transaction_date) as transaction_date,
        DATE(merchant_settlement_data.settlement_date) as settlement_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "REVERSE_REDEMPTION",
        count(merchant_settlement_data.original_transaction_id) as count_of_txn,
        sum(settlement_entries_data.total_receivable - settlement_entries_data.total_payable) as total_amount,
        sum(-1*settlement_entries_data.fee) as fee,
        sum(-1*settlement_entries_data.igst) as igst,
        sum(-1*settlement_entries_data.cgst) as cgst,
        sum(-1*settlement_entries_data.sgst) as sgst,
        sum(settlement_entries_data.amount) as net_amount,
        sum(merchant_settlement_data.transaction_amount) as amount_as_per_irctc,
        sum(settlement_entries_data.amount - merchant_settlement_data.transaction_amount) as diff,
        "Mismatch with {{MERCHANT_ID}}" as Reasons,
        2 as filter

FROM (select merchant_settlement_record.original_transaction_id,  sum(merchant_settlement_record.transaction_amount) as transaction_amount, max(merchant_settlement.created) as created, max(merchant_settlement.settlement_date) as settlement_date from torch.merchant_settlement_record as merchant_settlement_record
INNER JOIN torch.merchant_settlement as merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
where merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND merchant_settlement_record.transaction_date < DATE_ADD('{{END_DATE}}', {{BEFORE_DAY_RANGE}})
AND tenant.provider_name = '{{MERCHANT_ID}}'
group by original_transaction_id) merchant_settlement_data
INNER JOIN (select ledger_entries.original_transaction_id, max(settlement_entries.created) as created,
max(settlement_entries.transaction_date) as transaction_date, sum(settlement_entries.total_receivable) as total_receivable,
sum(settlement_entries.total_payable) as total_payable, sum(settlement_entries.fee) as fee, sum(settlement_entries.igst) as igst,
sum(settlement_entries.sgst) as sgst, sum(settlement_entries.cgst) as cgst, sum(settlement_entries.amount) as amount from accounting.ledger_entries AS ledger_entries
INNER JOIN accounting.settlement_entries AS settlement_entries on ledger_entries.settlement_entry_id = settlement_entries.id
left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
on settlement_entries.from_party_id = q2.id
where (q1.id is not null OR q2.id is not null)
AND ledger_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND ledger_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND ledger_entries.event_type = 'MERCHANT_FULFILMENT_REVERSAL'
AND settlement_entries.event_type = 'MERCHANT_FULFILMENT_REVERSAL'
AND settlement_entries.transaction_type = 'REDEMPTION_REVERSE'
AND settlement_entries.entry_type = 0
group by original_transaction_id ) settlement_entries_data
on merchant_settlement_data.original_transaction_id = settlement_entries_data.original_transaction_id
where ABS(merchant_settlement_data.transaction_amount - settlement_entries_data.amount) > 1
group by DATE(merchant_settlement_data.created), DATE(settlement_entries_data.created),  DATE(settlement_entries_data.transaction_date), DATE(merchant_settlement_data.settlement_date)

UNION

SELECT  DATE("") as file_upload_date,
        DATE(settlement_entries_data.created) as created_date,
        DATE(settlement_entries_data.transaction_date) as transaction_date,
        DATE("") as settlement_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "REVERSE_REDEMPTION",
        count(settlement_entries_data.original_transaction_id) as count_of_txn,
        sum(settlement_entries_data.total_receivable - settlement_entries_data.total_payable) as total_amount,
        sum(-1*settlement_entries_data.fee) as fee,
        sum(-1*settlement_entries_data.igst) as igst,
        sum(-1*settlement_entries_data.cgst) as cgst,
        sum(-1*settlement_entries_data.sgst) as sgst,
        sum(settlement_entries_data.amount) as net_amount,
        0 as amount_as_per_irctc,
        sum(settlement_entries_data.amount) as diff,
        "Missing in MIS report" as Reasons,
        3 as filter

FROM (select merchant_settlement_record.original_transaction_id,  sum(merchant_settlement_record.transaction_amount) as transaction_amount, max(merchant_settlement.created) as created, max(merchant_settlement.settlement_date) as settlement_date from torch.merchant_settlement_record as merchant_settlement_record
INNER JOIN torch.merchant_settlement as merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
where merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND merchant_settlement_record.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND tenant.provider_name = '{{MERCHANT_ID}}'
group by original_transaction_id) merchant_settlement_data
RIGHT JOIN (select ledger_entries.original_transaction_id, max(settlement_entries.created) as created,
max(settlement_entries.transaction_date) as transaction_date, sum(settlement_entries.total_receivable) as total_receivable,
sum(settlement_entries.total_payable) as total_payable, sum(settlement_entries.fee) as fee, sum(settlement_entries.igst) as igst,
sum(settlement_entries.sgst) as sgst, sum(settlement_entries.cgst) as cgst, sum(settlement_entries.amount) as amount from accounting.ledger_entries AS ledger_entries
INNER JOIN accounting.settlement_entries AS settlement_entries on ledger_entries.settlement_entry_id = settlement_entries.id
left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
on settlement_entries.from_party_id = q2.id
where (q1.id is not null OR q2.id is not null)
AND ledger_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND ledger_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND ledger_entries.event_type = 'MERCHANT_FULFILMENT_REVERSAL'
AND settlement_entries.event_type = 'MERCHANT_FULFILMENT_REVERSAL'
AND settlement_entries.transaction_type = 'REDEMPTION_REVERSE'
AND settlement_entries.entry_type = 0
group by original_transaction_id ) settlement_entries_data
on merchant_settlement_data.original_transaction_id = settlement_entries_data.original_transaction_id
where merchant_settlement_data.original_transaction_id IS NULL
group by DATE(settlement_entries_data.created),  DATE(settlement_entries_data.transaction_date)


UNION

SELECT  DATE(merchant_settlement_data.created) as file_upload_date,
        DATE("") as created_date,
        DATE("") as transaction_date,
        DATE(merchant_settlement_data.settlement_date) as settlement_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "REVERSE_REDEMPTION",
        count(merchant_settlement_data.original_transaction_id) as count_of_txn,
        0 as total_amount,
        0 as fee,
        0 as igst,
        0 as cgst,
        0 as sgst,
        0 as net_amount,
        sum(merchant_settlement_data.transaction_amount) as amount_as_per_irctc,
        sum(-1*merchant_settlement_data.transaction_amount) as diff,
        "Missing in Accounting" as Reasons,
        4 as filter


FROM (select merchant_settlement_record.original_transaction_id,  sum(merchant_settlement_record.transaction_amount) as transaction_amount, max(merchant_settlement.created) as created, max(merchant_settlement.settlement_date) as settlement_date from torch.merchant_settlement_record as merchant_settlement_record
INNER JOIN torch.merchant_settlement as merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
where merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND merchant_settlement_record.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND tenant.provider_name = '{{MERCHANT_ID}}'
group by original_transaction_id) merchant_settlement_data
LEFT JOIN (select ledger_entries.original_transaction_id, max(settlement_entries.created) as created,
max(settlement_entries.transaction_date) as transaction_date, sum(settlement_entries.total_receivable) as total_receivable,
sum(settlement_entries.total_payable) as total_payable, sum(settlement_entries.fee) as fee, sum(settlement_entries.igst) as igst,
sum(settlement_entries.sgst) as sgst, sum(settlement_entries.cgst) as cgst, sum(settlement_entries.amount) as amount from accounting.ledger_entries AS ledger_entries
INNER JOIN accounting.settlement_entries AS settlement_entries on ledger_entries.settlement_entry_id = settlement_entries.id
left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
on settlement_entries.from_party_id = q2.id
where (q1.id is not null OR q2.id is not null)
AND ledger_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND ledger_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND ledger_entries.event_type = 'MERCHANT_FULFILMENT_REVERSAL'
AND settlement_entries.event_type = 'MERCHANT_FULFILMENT_REVERSAL'
AND settlement_entries.transaction_type = 'REDEMPTION_REVERSE'
AND settlement_entries.entry_type = 0
group by original_transaction_id ) settlement_entries_data
on merchant_settlement_data.original_transaction_id = settlement_entries_data.original_transaction_id
where settlement_entries_data.original_transaction_id is NULL
group by DATE(merchant_settlement_data.created), DATE(merchant_settlement_data.settlement_date)

ORDER BY filter, file_upload_date, created_date, transaction_date, settlement_date
