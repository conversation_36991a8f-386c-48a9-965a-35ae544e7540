SELECT settlement_entries.external_transaction_id as nexusId,merchant_settlement_record.transaction_id, merchant_settlement_record.external_transaction_id as mis_external_id, "FORWARD" as transaction_type, settlement_entries.total_payable  as total_payable, merchant_settlement_record.transaction_amount, merchant_settlement_record.transaction_date,settlement_entries.transaction_date as settlement_date
FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
(Select * from nexus.provider_reference_mappings as nexus_provider_reference_mappings
where nexus_provider_reference_mappings.year in ({{YEARS}})
AND nexus_provider_reference_mappings.month in ({{MONTHS}}) and nexus_provider_reference_mappings.service_provider_id = '{{MERCHANT_ID}}' and nexus_provider_reference_mappings.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
and nexus_provider_reference_mappings.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) nexus_provider_reference_mappings
ON merchant_settlement_record.external_transaction_id = nexus_provider_reference_mappings.provider_reference
INNER JOIN
(Select max(created) as created, max(transaction_date) as transaction_date, external_transaction_id, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable, sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(cgst) as cgst, sum(sgst) as sgst from accounting.settlement_entries
left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id in ('VIINAPPAP','VIINAPPDEL','VIINAPPUPE','VIINAPPGUJ','VIINAPPHAR','VIINAPPKAR','VIINAPPKOL','VIINAPPMUM','VIINAPPRAJ','VIINAPPWB','VIINAPPPJB','VIINAPPUPW','VIINAPPMAG','VIINAPPTN','VIINAPPKER','VIINAPPORS','VIINAPPASM','VIINAPPNES','VIINAPPBHR','VIINAPPMPC','VIINAPPHP','VIINAPPJNK')) q1
on settlement_entries.to_party_id = q1.id
left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id in ('VIINAPPAP','VIINAPPDEL','VIINAPPUPE','VIINAPPGUJ','VIINAPPHAR','VIINAPPKAR','VIINAPPKOL','VIINAPPMUM','VIINAPPRAJ','VIINAPPWB','VIINAPPPJB','VIINAPPUPW','VIINAPPMAG','VIINAPPTN','VIINAPPKER','VIINAPPORS','VIINAPPASM','VIINAPPNES','VIINAPPBHR','VIINAPPMPC','VIINAPPHP','VIINAPPJNK')) q2
on settlement_entries.from_party_id = q2.id
where (q1.id is not null OR q2.id is not null)
AND settlement_entries.year in ({{YEARS}})
AND settlement_entries.month in ({{MONTHS}})
AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND settlement_entries.event_type != 'MERCHANT_FULFILMENT_REVERSAL'
group by external_transaction_id) settlement_entries
ON nexus_provider_reference_mappings.reference_id = settlement_entries.external_transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
WHERE merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND ABS(merchant_settlement_record.transaction_amount - settlement_entries.total_payable) >= 1
AND merchant_settlement_record.transaction_type = 0
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND tenant.provider_name = '{{MERCHANT_ID}}'
