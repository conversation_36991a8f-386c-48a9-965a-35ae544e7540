SELECT  DATE(settlement_entries.created) as settlement_date,
        DATE(settlement_entries.transaction_date) as transaction_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "Redemption",
        count(merchant_settlement_record.external_transaction_id) as count_of_txn,
        sum(settlement_entries.total_payable) as total_amount,
        sum(-1*settlement_entries.fee) as fee,
        sum(-1*settlement_entries.igst) as igst,
        sum(-1*settlement_entries.cgst) as cgst,
        sum(-1*settlement_entries.sgst) as sgst,
        sum(settlement_entries.amount) as net_amount,
        sum(merchant_settlement_record.transaction_amount) as amount_as_per_provider,
        sum(settlement_entries.total_payable - merchant_settlement_record.transaction_amount) as diff,
        "Matching with {{MERCHANT_ID}}" as Reasons,
        1 as filter


FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
(Select max(created) as created, max(transaction_date) as transaction_date, external_transaction_id, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable, sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(cgst) as cgst, sum(sgst) as sgst from accounting.settlement_entries
left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
on settlement_entries.from_party_id = q2.id
where (q1.id is not null OR q2.id is not null)
AND settlement_entries.year in ({{YEARS}})
AND settlement_entries.month in ({{MONTHS}})
AND settlement_entries.transaction_type= 'REDEMPTION'
AND settlement_entries.entry_type = 1
AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND settlement_entries.event_type != 'MERCHANT_FULFILMENT_REVERSAL'
group by external_transaction_id) settlement_entries
ON merchant_settlement_record.external_transaction_id = settlement_entries.external_transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
WHERE ABS(merchant_settlement_record.transaction_amount - settlement_entries.total_payable) < 1
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type = 0
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND tenant.provider_name = '{{MERCHANT_ID}}'
group by DATE(settlement_entries.created),  DATE(settlement_entries.transaction_date)

UNION

SELECT  DATE(settlement_entries.created) as settlement_date,
        DATE(settlement_entries.transaction_date) as transaction_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "Redemption",
        count(merchant_settlement_record.external_transaction_id) as count_of_txn,
        sum(settlement_entries.total_payable) as total_amount,
        sum(-1*settlement_entries.fee) as fee,
        sum(-1*settlement_entries.igst) as igst,
        sum(-1*settlement_entries.cgst) as cgst,
        sum(-1*settlement_entries.sgst) as sgst,
        sum(settlement_entries.amount) as net_amount,
        sum(merchant_settlement_record.transaction_amount) as amount_as_per_provider,
        sum(settlement_entries.total_payable - merchant_settlement_record.transaction_amount) as diff,
        "Mismatch with {{MERCHANT_ID}}" as Reasons,
        2 as filter

FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
(Select max(created) as created, max(transaction_date) as transaction_date, external_transaction_id, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable, sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(cgst) as cgst, sum(sgst) as sgst from accounting.settlement_entries
left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
on settlement_entries.from_party_id = q2.id
where (q1.id is not null OR q2.id is not null)
AND settlement_entries.year in ({{YEARS}})
AND settlement_entries.month in ({{MONTHS}})
AND settlement_entries.transaction_type= 'REDEMPTION'
AND settlement_entries.entry_type = 1
AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND settlement_entries.event_type != 'MERCHANT_FULFILMENT_REVERSAL'
group by external_transaction_id) settlement_entries
ON merchant_settlement_record.external_transaction_id = settlement_entries.external_transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
WHERE ABS(merchant_settlement_record.transaction_amount - settlement_entries.total_payable) >= 1
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type = 0
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND tenant.provider_name = '{{MERCHANT_ID}}'
group by  DATE(settlement_entries.created),  DATE(settlement_entries.transaction_date)


UNION

SELECT  DATE(settlement_entries.created) as settlement_date,
        DATE(settlement_entries.transaction_date) as transaction_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "Redemption",
        count(settlement_entries.external_transaction_id) as count_of_txn,
        sum(settlement_entries.total_payable) as total_amount,
        sum(-1*settlement_entries.fee) as fee,
        sum(-1*settlement_entries.igst) as igst,
        sum(-1*settlement_entries.cgst) as cgst,
        sum(-1*settlement_entries.sgst) as sgst,
        sum(settlement_entries.amount) as net_amount,
        0 as amount_as_per_provider,
        sum(settlement_entries.total_payable) as diff,
        "Missing in MIS report" as Reasons,
        3 as filter
from (Select max(created) as created, max(transaction_date) as transaction_date, external_transaction_id, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable, sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(cgst) as cgst, sum(sgst) as sgst from accounting.settlement_entries
left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
on settlement_entries.from_party_id = q2.id
where (q1.id is not null OR q2.id is not null)
AND settlement_entries.year in ({{YEARS}})
AND settlement_entries.month in ({{MONTHS}})
AND settlement_entries.transaction_type= 'REDEMPTION'
AND settlement_entries.entry_type = 1
AND settlement_entries.transaction_date >= '{{START_DATE}}'
AND settlement_entries.transaction_date < '{{END_DATE}}'
AND settlement_entries.event_type != 'MERCHANT_FULFILMENT_REVERSAL'
group by external_transaction_id) settlement_entries
LEFT JOIN (select external_transaction_id from torch.merchant_settlement_record
where  merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}}) AND transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND merchant_settlement_record.status != '24') merchant_settlement_record
ON merchant_settlement_record.external_transaction_id = settlement_entries.external_transaction_id
where merchant_settlement_record.external_transaction_id IS NULL
group by DATE(settlement_entries.created),  DATE(settlement_entries.transaction_date)


UNION

SELECT  DATE(merchant_settlement.settlement_date) as settlement_date,
        DATE("") as transaction_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "Redemption",
        count(merchant_settlement_record.external_transaction_id) as count_of_txn,
        0 as total_amount,
        0 as fee,
        0 as igst,
        0 as cgst,
        0 as sgst,
        0 as net_amount,
        sum(merchant_settlement_record.transaction_amount) as amount_as_per_provider,
        sum(-1*merchant_settlement_record.transaction_amount) as diff,
        "Missing in Accounting" as Reasons,
        4 as filter


FROM torch.merchant_settlement_record AS merchant_settlement_record
LEFT JOIN
(Select max(created) as created, max(transaction_date) as transaction_date, external_transaction_id, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable, sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(cgst) as cgst, sum(sgst) as sgst from accounting.settlement_entries
left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
on settlement_entries.from_party_id = q2.id
where (q1.id is not null OR q2.id is not null)
AND settlement_entries.year in ({{YEARS}})
AND settlement_entries.month in ({{MONTHS}})
AND settlement_entries.transaction_type= 'REDEMPTION'
AND settlement_entries.entry_type = 1
AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND settlement_entries.event_type != 'MERCHANT_FULFILMENT_REVERSAL'
group by external_transaction_id) settlement_entries
ON merchant_settlement_record.external_transaction_id = settlement_entries.external_transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
where settlement_entries.external_transaction_id IS NULL
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type in ({{TRANSACTION_TYPES}})
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND tenant.provider_name = '{{MERCHANT_ID}}'
group by DATE(merchant_settlement.created), DATE(merchant_settlement.settlement_date)
ORDER BY filter,settlement_date, transaction_date