SELECT bbps_transactions.client_reference_id as nexusId,merchant_settlement_record.external_transaction_id as mis_external_id, "" as settlement_external_id, "FORWARD" AS transaction_type, "Missing in BBPS - Present in MIS" AS reason, 0 as settlement_total_payable, merchant_settlement_record.transaction_amount as mis_amount, merchant_settlement_record.transaction_date as mis_date,merchant_settlement_record.transaction_date as settlement_date
FROM torch.merchant_settlement_record AS merchant_settlement_record
LEFT JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
where (bbps_transactions.transaction_id IS NULL OR bbps_transactions.client_reference_id is NULL)
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type = 0
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND UPPER(tenant.provider_name) = '{{MERCHANT_ID}}'

UNION

SELECT bbps_transactions.client_reference_id as nexusId,merchant_settlement_record.external_transaction_id as mis_external_id, settlement_entries.external_transaction_id as settlement_external_id, "FORWARD" AS transaction_type, "Missing in accounting" AS reason, settlement_entries.amount as settlement_total_payable, merchant_settlement_record.transaction_amount as mis_amount,merchant_settlement_record.transaction_date as mis_date,settlement_entries.transaction_date as settlement_date
FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
LEFT JOIN
(Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
 sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(sgst) as sgst, sum(cgst) as cgst, sum(commission) as commission from accounting.settlement_entries AS settlement_entries
	left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
    on settlement_entries.to_party_id = q1.id
    left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
    on settlement_entries.from_party_id = q2.id
    where (q1.id is not null OR q2.id is not null)
    AND settlement_entries.year in ({{YEARS}})
    AND settlement_entries.month in ({{MONTHS}})
	and ((settlement_entries.transaction_type = 'REDEMPTION' and settlement_entries.entry_type = 1) OR
	(settlement_entries.transaction_type = 'REVENUE' and settlement_entries.entry_type = 0) OR
	(settlement_entries.transaction_type = 'COST' and settlement_entries.entry_type = 1))
	and settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
	and settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
	group by external_transaction_id
) settlement_entries
ON bbps_transactions.client_reference_id = settlement_entries.external_transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
where settlement_entries.external_transaction_id IS NULL
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type = 0
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND tenant.provider_name = '{{MERCHANT_ID}}'

UNION

SELECT bbps_transactions.client_reference_id as nexusId,"" as mis_external_id, settlement_entries.external_transaction_id as settlement_external_id, "FORWARD" as transaction_type, "Missing in BBPS - Present in Accounting" as reason, settlement_entries.amount as settlement_total_payable,0 as mis_amount, settlement_entries.transaction_date as mis_date,settlement_entries.transaction_date as settlement_date
FROM
(Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
 sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(sgst) as sgst, sum(cgst) as cgst, sum(commission) as commission from accounting.settlement_entries AS settlement_entries
	left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
    on settlement_entries.to_party_id = q1.id
    left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
    on settlement_entries.from_party_id = q2.id
    where (q1.id is not null OR q2.id is not null)
    AND settlement_entries.year in ({{YEARS}})
    AND settlement_entries.month in ({{MONTHS}})
	and ((settlement_entries.transaction_type = 'REDEMPTION' and settlement_entries.entry_type = 1) OR
	(settlement_entries.transaction_type = 'REVENUE' and settlement_entries.entry_type = 0) OR
	(settlement_entries.transaction_type = 'COST' and settlement_entries.entry_type = 1))
	and settlement_entries.transaction_date >= '{{START_DATE}}'
	and settlement_entries.transaction_date < '{{END_DATE}}'
	group by external_transaction_id
) settlement_entries
LEFT JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON settlement_entries.external_transaction_id = bbps_transactions.client_reference_id
where (bbps_transactions.transaction_id IS NULL OR bbps_transactions.client_reference_id IS NULL)

UNION

SELECT bbps_transactions.client_reference_id as nexusId,merchant_settlement_record.external_transaction_id as mis_external_id, settlement_entries.external_transaction_id as settlement_external_id, "FORWARD" as transaction_type, "Missing in MIS" as reason, settlement_entries.amount as settlement_total_payable,merchant_settlement_record.transaction_amount as mis_amount, merchant_settlement_record.transaction_date as mis_date,settlement_entries.transaction_date as settlement_date
FROM (Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
 sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(sgst) as sgst, sum(cgst) as cgst, sum(commission) as commission from accounting.settlement_entries AS settlement_entries
	left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
    on settlement_entries.to_party_id = q1.id
    left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
    on settlement_entries.from_party_id = q2.id
    where (q1.id is not null OR q2.id is not null)
    AND settlement_entries.year in ({{YEARS}})
    AND settlement_entries.month in ({{MONTHS}})
	and ((settlement_entries.transaction_type = 'REDEMPTION' and settlement_entries.entry_type = 1) OR
	(settlement_entries.transaction_type = 'REVENUE' and settlement_entries.entry_type = 0) OR
	(settlement_entries.transaction_type = 'COST' and settlement_entries.entry_type = 1))
	and settlement_entries.transaction_date >= '{{START_DATE}}'
	and settlement_entries.transaction_date < '{{END_DATE}}'
	group by external_transaction_id
) settlement_entries
INNER JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON settlement_entries.external_transaction_id = bbps_transactions.client_reference_id
LEFT JOIN
(Select * from torch.merchant_settlement_record as merchant_settlement_record
where merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
and merchant_settlement_record.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
and merchant_settlement_record.transaction_type = 0
AND merchant_settlement_record.status != '24'
) merchant_settlement_record
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
WHERE merchant_settlement_record.transaction_id IS NULL

UNION

SELECT bbps_transactions.client_reference_id as nexusId,merchant_settlement_record.external_transaction_id as mis_external_id, "" as settlement_external_id, "REVERSE" AS transaction_type, "Missing in BBPS - Present in MIS" AS reason, 0 as settlement_total_payable, merchant_settlement_record.transaction_amount as mis_amount, merchant_settlement_record.transaction_date as mis_date,merchant_settlement_record.transaction_date as settlement_date
FROM torch.merchant_settlement_record AS merchant_settlement_record
LEFT JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}}) and bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
where (bbps_transactions.transaction_id IS NULL OR bbps_transactions.client_reference_id IS NULL)
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND UPPER(tenant.provider_name) = '{{MERCHANT_ID}}'

UNION

SELECT bbps_transactions.client_reference_id as nexusId,merchant_settlement_record.external_transaction_id as mis_external_id, settlement_entries.external_transaction_id as settlement_external_id, "REVERSE_REVENUE" AS transaction_type, "Missing in accounting" AS reason, settlement_entries.amount as settlement_total_payable, merchant_settlement_record.transaction_amount as mis_amount,merchant_settlement_record.transaction_date as mis_date,settlement_entries.transaction_date as settlement_date
FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
LEFT JOIN
(Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(sgst) as sgst, sum(cgst) as cgst, sum(commission) as commission from accounting.settlement_entries AS settlement_entries
	left outer join (SELECT id from valhalla_v2.merchants where merchant_id = '{{MERCHANT_ID}}') q1
	on settlement_entries.to_party_id = q1.id
	where (q1.id is not null)
	AND settlement_entries.year in ({{YEARS}})
    AND settlement_entries.month in ({{MONTHS}})
	AND settlement_entries.transaction_type = 'REVENUE'
	AND settlement_entries.entry_type = 1
	AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
	AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
	group by external_transaction_id
) settlement_entries
ON bbps_transactions.client_reference_id = settlement_entries.external_transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
where settlement_entries.external_transaction_id IS NULL
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type IN (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND UPPER(tenant.provider_name) = '{{MERCHANT_ID}}'

UNION

SELECT bbps_transactions.client_reference_id as nexusId,"" as mis_external_id, settlement_entries.external_transaction_id as settlement_external_id, "REVERSE_REVENUE" as transaction_type, "Missing in BBPS - Present in Accounting" as reason, settlement_entries.amount as settlement_total_payable,0 as mis_amount, settlement_entries.transaction_date as mis_date,settlement_entries.transaction_date as settlement_date
FROM (Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
sum(amount) as amount, sum(fee) as fee, sum(commission) as commission, sum(igst) as igst, sum(cgst) as cgst, sum(sgst) as sgst from accounting.settlement_entries AS settlement_entries
LEFT JOIN (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
WHERE settlement_entries.year in ({{YEARS}})
AND settlement_entries.month in ({{MONTHS}})
AND settlement_entries.transaction_type = 'REVENUE'
AND settlement_entries.entry_type = 1
AND q1.id is not null
AND settlement_entries.transaction_date >= '{{START_DATE}}'
AND settlement_entries.transaction_date < '{{END_DATE}}'
group by external_transaction_id) settlement_entries
LEFT JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}}) and bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON settlement_entries.external_transaction_id = bbps_transactions.client_reference_id
WHERE (bbps_transactions.transaction_id IS NULL OR bbps_transactions.client_reference_id IS NULL)

UNION

SELECT bbps_transactions.client_reference_id as nexusId,merchant_settlement_record.external_transaction_id as mis_external_id, settlement_entries.external_transaction_id as settlement_external_id, "REVERSE_REVENUE" as transaction_type, "Missing in MIS" as reason, settlement_entries.amount as settlement_total_payable,merchant_settlement_record.transaction_amount as mis_amount, merchant_settlement_record.transaction_date as mis_date,settlement_entries.transaction_date as settlement_date
FROM (Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
sum(amount) as amount, sum(fee) as fee, sum(commission) as commission, sum(igst) as igst, sum(cgst) as cgst, sum(sgst) as sgst from accounting.settlement_entries AS settlement_entries
LEFT JOIN (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
WHERE settlement_entries.year in ({{YEARS}})
AND settlement_entries.month in ({{MONTHS}}) and settlement_entries.transaction_type = 'REVENUE'
AND settlement_entries.entry_type = 1
AND q1.id is not null
AND settlement_entries.transaction_date >= '{{START_DATE}}'
AND settlement_entries.transaction_date < '{{END_DATE}}'
group by external_transaction_id) settlement_entries
INNER JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}}) and  bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON settlement_entries.external_transaction_id = bbps_transactions.client_reference_id
LEFT JOIN
(Select * from torch.merchant_settlement_record as merchant_settlement_record
where merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}}) and  merchant_settlement_record.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
and merchant_settlement_record.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
and merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
) merchant_settlement_record
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
WHERE (merchant_settlement_record.transaction_id IS NULL)

UNION

SELECT bbps_transactions.client_reference_id as nexusId,merchant_settlement_record.external_transaction_id as mis_external_id, "" as settlement_external_id, "REVERSE_REDEMPTION" as transaction_type, "Missing in nexus reversals - Present in MIS" as reason, 0 as settlement_total_payable,merchant_settlement_record.transaction_amount as mis_amount, merchant_settlement_record.transaction_date as mis_date,merchant_settlement_record.transaction_date as settlement_date
FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}}) and  bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
LEFT JOIN
(Select * from nexus.reversed_transactions as nexus_reversals
 where nexus_reversals.year in ({{YEARS}})
AND nexus_reversals.month in ({{MONTHS}}) and  nexus_reversals.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
and nexus_reversals.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) nexus_reversals
ON bbps_transactions.client_reference_id = nexus_reversals.original_transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
WHERE (nexus_reversals.original_transaction_id IS NULL OR nexus_reversals.reversal_id IS NULL)
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type IN (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND UPPER(tenant.provider_name) = '{{MERCHANT_ID}}'

UNION

SELECT bbps_transactions.client_reference_id as nexusId,merchant_settlement_record.external_transaction_id as mis_external_id, settlement_entries.external_transaction_id as settlement_external_id, "REVERSE_REDEMPTION" AS transaction_type, "Missing in accounting" AS reason, settlement_entries.amount as settlement_total_payable, merchant_settlement_record.transaction_amount as mis_amount,merchant_settlement_record.transaction_date as mis_date,settlement_entries.transaction_date as settlement_date
FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
INNER JOIN
(Select * from nexus.reversed_transactions as nexus_reversals
 where nexus_reversals.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND nexus_reversals.year in ({{YEARS}})
AND nexus_reversals.month in ({{MONTHS}})
and nexus_reversals.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) nexus_reversals
ON bbps_transactions.client_reference_id = nexus_reversals.original_transaction_id
LEFT JOIN
(Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(sgst) as sgst, sum(cgst) as cgst, sum(commission) as commission from accounting.settlement_entries AS settlement_entries
	left outer join (SELECT id from valhalla_v2.merchants where merchant_id = '{{MERCHANT_ID}}') q1
	on settlement_entries.from_party_id = q1.id
	where (q1.id is not null)
	AND settlement_entries.year in ({{YEARS}})
    AND settlement_entries.month in ({{MONTHS}})
	AND settlement_entries.transaction_type = 'REDEMPTION_REVERSE'
	AND settlement_entries.entry_type = 0
	AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
	AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
	group by external_transaction_id
) settlement_entries
ON nexus_reversals.reversal_id = settlement_entries.external_transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
where settlement_entries.external_transaction_id IS NULL
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND UPPER(tenant.provider_name) = '{{MERCHANT_ID}}'

UNION

SELECT "" as nexusId,"" as mis_external_id, settlement_entries.external_transaction_id as settlement_external_id, "REVERSE_REDEMPTION" as transaction_type, "Missing in nexus table - Present in accounting" as reason, settlement_entries.amount as settlement_total_payable,0 as mis_amount, settlement_entries.transaction_date as mis_date,settlement_entries.transaction_date as settlement_date
FROM (Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
sum(amount) as amount, sum(fee) as fee, sum(commission) as commission, sum(igst) as igst, sum(cgst) as cgst, sum(sgst) as sgst from accounting.settlement_entries AS settlement_entries
LEFT JOIN (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.from_party_id = q1.id
WHERE settlement_entries.transaction_type = 'REDEMPTION_REVERSE'
AND settlement_entries.year in ({{YEARS}})
AND settlement_entries.month in ({{MONTHS}})
AND settlement_entries.entry_type = 0
AND q1.id is not null
AND settlement_entries.transaction_date >= '{{START_DATE}}'
AND settlement_entries.transaction_date < '{{END_DATE}}'
group by external_transaction_id) settlement_entries
LEFT JOIN
(Select * from nexus.reversed_transactions as nexus_reversals
 where nexus_reversals.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND nexus_reversals.year in ({{YEARS}})
AND nexus_reversals.month in ({{MONTHS}})
and nexus_reversals.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) nexus_reversals
ON settlement_entries.external_transaction_id = nexus_reversals.reversal_id
where (nexus_reversals.original_transaction_id IS NULL OR nexus_reversals.reversal_id IS NULL)

UNION

SELECT bbps_transactions.client_reference_id as nexusId,"" as mis_external_id, settlement_entries.external_transaction_id as settlement_external_id, "REVERSE_REDEMPTION" as transaction_type, "Missing in BBPS - Present in Accounting" as reason, settlement_entries.amount as settlement_total_payable,0 as mis_amount, settlement_entries.transaction_date as mis_date,settlement_entries.transaction_date as settlement_date
FROM (Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
sum(amount) as amount, sum(fee) as fee, sum(commission) as commission, sum(igst) as igst, sum(cgst) as cgst, sum(sgst) as sgst from accounting.settlement_entries AS settlement_entries
LEFT JOIN (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.from_party_id = q1.id
WHERE settlement_entries.transaction_type = 'REDEMPTION_REVERSE'
AND settlement_entries.entry_type = 0
AND settlement_entries.year in ({{YEARS}})
AND settlement_entries.month in ({{MONTHS}})
AND q1.id is not null
AND settlement_entries.transaction_date >= '{{START_DATE}}'
AND settlement_entries.transaction_date < '{{END_DATE}}'
group by external_transaction_id) settlement_entries
INNER JOIN
(Select * from nexus.reversed_transactions as nexus_reversals
 where nexus_reversals.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND nexus_reversals.year in ({{YEARS}})
AND nexus_reversals.month in ({{MONTHS}})
and nexus_reversals.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) nexus_reversals
ON settlement_entries.external_transaction_id = nexus_reversals.reversal_id
LEFT JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON bbps_transactions.client_reference_id = nexus_reversals.original_transaction_id
where (bbps_transactions.client_reference_id IS NULL OR bbps_transactions.transaction_id IS NULL)

UNION

SELECT bbps_transactions.client_reference_id as nexusId,merchant_settlement_record.external_transaction_id as mis_external_id, settlement_entries.external_transaction_id as settlement_external_id, "REVERSE_REDEMPTION" as transaction_type, "Missing in MIS" as reason, settlement_entries.amount as settlement_total_payable,merchant_settlement_record.transaction_amount as mis_amount, merchant_settlement_record.transaction_date as mis_date,settlement_entries.transaction_date as settlement_date
FROM (Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
sum(amount) as amount, sum(fee) as fee, sum(commission) as commission, sum(igst) as igst, sum(cgst) as cgst, sum(sgst) as sgst from accounting.settlement_entries AS settlement_entries
LEFT JOIN (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.from_party_id = q1.id
WHERE settlement_entries.transaction_type = 'REDEMPTION_REVERSE'
AND settlement_entries.entry_type = 0
AND settlement_entries.year in ({{YEARS}})
AND settlement_entries.month in ({{MONTHS}})
AND q1.id is not null
AND settlement_entries.transaction_date >= '{{START_DATE}}'
AND settlement_entries.transaction_date < '{{END_DATE}}'
group by external_transaction_id) settlement_entries
INNER JOIN
(Select * from nexus.reversed_transactions as nexus_reversals
 where nexus_reversals.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND nexus_reversals.year in ({{YEARS}})
AND nexus_reversals.month in ({{MONTHS}})
and nexus_reversals.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) nexus_reversals
ON settlement_entries.external_transaction_id = nexus_reversals.reversal_id
INNER JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON bbps_transactions.client_reference_id = nexus_reversals.original_transaction_id
LEFT JOIN
(Select * from torch.merchant_settlement_record as merchant_settlement_record
where merchant_settlement_record.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
and merchant_settlement_record.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
and merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
) merchant_settlement_record
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
WHERE (merchant_settlement_record.transaction_id IS NULL)
