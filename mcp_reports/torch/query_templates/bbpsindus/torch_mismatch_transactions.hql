SELECT bbps_transactions.client_reference_id as nexusId , merchant_settlement_record.transaction_id, settlement_entries.external_transaction_id, "FORWARD" as transaction_type, merchant_settlement_record.transaction_date as mis_date,settlement_entries.transaction_date as settlement_date, settlement_entries.total_payable,
(merchant_settlement_record.transaction_amount + merchant_settlement_record.fee + merchant_settlement_record.igst + merchant_settlement_record.cgst + merchant_settlement_record.sgst)
FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
INNER JOIN
(Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
 sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(sgst) as sgst, sum(cgst) as cgst, sum(commission) as commission from accounting.settlement_entries AS settlement_entries
	left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
    on settlement_entries.to_party_id = q1.id
    left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
    on settlement_entries.from_party_id = q2.id
    where (q1.id is not null OR q2.id is not null)
    AND settlement_entries.year in ({{YEARS}})
    AND settlement_entries.month in ({{MONTHS}})
	and ((settlement_entries.transaction_type = 'REDEMPTION' and settlement_entries.entry_type = 1) OR
	(settlement_entries.transaction_type = 'REVENUE' and settlement_entries.entry_type = 0) OR
	(settlement_entries.transaction_type = 'COST' and settlement_entries.entry_type = 1))
	and settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
	and settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
	group by external_transaction_id
) settlement_entries
ON bbps_transactions.client_reference_id = settlement_entries.external_transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
WHERE ABS(merchant_settlement_record.transaction_amount + merchant_settlement_record.fee + merchant_settlement_record.igst + merchant_settlement_record.cgst
 + merchant_settlement_record.sgst - (settlement_entries.total_receivable - settlement_entries.total_payable)) >= 1
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type = 0
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND UPPER(tenant.provider_name) = '{{MERCHANT_ID}}'

UNION

SELECT bbps_transactions.client_reference_id as nexusId ,merchant_settlement_record.transaction_id, settlement_entries.external_transaction_id, "REVERSE_REVENUE" as transaction_type, merchant_settlement_record.transaction_date  as mis_date,settlement_entries.transaction_date as settlement_date, settlement_entries.amount,
(merchant_settlement_record.fee + merchant_settlement_record.igst + merchant_settlement_record.cgst + merchant_settlement_record.sgst)
FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
INNER JOIN
(Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
 sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(sgst) as sgst, sum(cgst) as cgst, sum(commission) as commission from accounting.settlement_entries AS settlement_entries
	left outer join (SELECT id from valhalla_v2.merchants where merchant_id = '{{MERCHANT_ID}}') q1
	on settlement_entries.to_party_id = q1.id
	where (q1.id is not null)
	AND settlement_entries.year in ({{YEARS}})
    AND settlement_entries.month in ({{MONTHS}})
	AND settlement_entries.transaction_type = 'REVENUE'
	AND settlement_entries.entry_type = 1
	AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
	AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
	group by external_transaction_id
) settlement_entries
ON bbps_transactions.client_reference_id = settlement_entries.external_transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
WHERE ABS(merchant_settlement_record.fee + merchant_settlement_record.cgst + merchant_settlement_record.igst + merchant_settlement_record.sgst + settlement_entries.amount) >= 1
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND UPPER(tenant.provider_name) = '{{MERCHANT_ID}}'

UNION

SELECT bbps_transactions.client_reference_id as nexusId ,merchant_settlement_record.transaction_id, settlement_entries.external_transaction_id, "REVERSE_REDEMPTION" as transaction_type, merchant_settlement_record.transaction_date as mis_date,settlement_entries.transaction_date as settlement_date, settlement_entries.amount,
merchant_settlement_record.transaction_amount
FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
INNER JOIN
(Select * from nexus.reversed_transactions as nexus_reversals
where nexus_reversals.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND nexus_reversals.year in ({{YEARS}})
AND nexus_reversals.month in ({{MONTHS}})
and nexus_reversals.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) nexus_reversals
ON bbps_transactions.client_reference_id = nexus_reversals.original_transaction_id
INNER JOIN
(Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
 sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(sgst) as sgst, sum(cgst) as cgst, sum(commission) as commission from accounting.settlement_entries AS settlement_entries
	left outer join (Select id from valhalla_v2.merchants where merchant_id = '{{MERCHANT_ID}}') q1
	on settlement_entries.from_party_id = q1.id
	where (q1.id is not null)
	AND settlement_entries.year in ({{YEARS}})
    AND settlement_entries.month in ({{MONTHS}})
	and settlement_entries.transaction_type = 'REDEMPTION_REVERSE'
	and settlement_entries.entry_type = 0
	and settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
	and settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
	group by external_transaction_id
) settlement_entries
ON nexus_reversals.reversal_id = settlement_entries.external_transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
WHERE ABS(merchant_settlement_record.transaction_amount - settlement_entries.amount) >= 1
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND UPPER(tenant.provider_name) = '{{MERCHANT_ID}}'
