SELECT merchant_settlement_record.transaction_id, merchant_settlement_record.external_transaction_id, merchant_settlement_record.original_transaction_id, "FORWARD" as transaction_type, merchant_settlement_record.transaction_date, merchant_settlement_record.transaction_amount
FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
 (
 SELECT transaction_id from
 torch.merchant_settlement_record AS merchant_settlement_record
 INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
 INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
 WHERE tenant.provider_name='{{MERCHANT_ID}}'
 AND merchant_settlement_record.year in ({{YEARS}})
 AND merchant_settlement_record.month in ({{MONTHS}})
 AND merchant_settlement_record.transaction_type = 0
 AND merchant_settlement_record.status != '24'
 AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
 AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
 MINUS
 SELECT transaction_id FROM
 torch.merchant_settlement_record AS merchant_settlement_record
 INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
 INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
 WHERE  tenant.provider_name=concat('{{MERCHANT_ID}}','_DELTA')
 AND merchant_settlement_record.year in ({{YEARS}})
 AND merchant_settlement_record.month in ({{MONTHS}})
 AND merchant_settlement_record.transaction_type = 0
 AND merchant_settlement_record.status != '24'
 AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
 AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
 )  irctc_mis
 on (merchant_settlement_record.transaction_id=irctc_mis.transaction_id)
 INNER JOIN
(Select max(created) as created, max(transaction_date) as transaction_date, transaction_id, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable, sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(cgst) as cgst, sum(sgst) as sgst from accounting.settlement_entries
left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
on settlement_entries.from_party_id = q2.id
where (q1.id is not null OR q2.id is not null)
AND settlement_entries.year in ({{YEARS}})
AND settlement_entries.month in ({{MONTHS}})
AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND settlement_entries.event_type != 'MERCHANT_FULFILMENT_REVERSAL'
group by transaction_id) settlement_entries
ON merchant_settlement_record.transaction_id = settlement_entries.transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
WHERE ABS(merchant_settlement_record.transaction_amount - settlement_entries.amount) < 1
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type = 0
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND tenant.provider_name = '{{MERCHANT_ID}}'


UNION

SELECT merchant_settlement_record.transaction_id, merchant_settlement_record.external_transaction_id, merchant_settlement_record.original_transaction_id, "REVERSE" as transaction_type, merchant_settlement_record.transaction_date, merchant_settlement_record.transaction_amount
FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
(Select max(created) as created, max(transaction_date) as transaction_date, external_transaction_id, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable, sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(cgst) as cgst, sum(sgst) as sgst from accounting.settlement_entries
left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
on settlement_entries.from_party_id = q2.id
where (q1.id is not null OR q2.id is not null)
AND settlement_entries.year in ({{YEARS}})
AND settlement_entries.month in ({{MONTHS}})
AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND settlement_entries.event_type = 'MERCHANT_FULFILMENT_REVERSAL'
group by external_transaction_id) settlement_entries
ON merchant_settlement_record.external_transaction_id = settlement_entries.external_transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
WHERE ABS(merchant_settlement_record.transaction_amount - settlement_entries.amount) < 1
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND tenant.provider_name = '{{MERCHANT_ID}}'
