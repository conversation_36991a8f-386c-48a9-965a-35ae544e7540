SELECT  DATE(merchant_settlement.settlement_date) as settlement_date,
        DATE(merchant_settlement_record.transaction_date) as transaction_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "REVERSE_REVENUE",
        count(merchant_settlement_record.external_transaction_id) as count_of_txn,
        sum(settlement_entries.amount) as total_amount,
        sum(settlement_entries.fee) as fee,
        sum(settlement_entries.igst) as igst,
        sum(settlement_entries.cgst) as cgst,
        sum(settlement_entries.sgst) as sgst,
        sum(settlement_entries.amount) as net_amount,
        sum(merchant_settlement_record.fee + merchant_settlement_record.cgst + merchant_settlement_record.igst + merchant_settlement_record.sgst) as amount_as_per_provider,
        sum(merchant_settlement_record.fee + merchant_settlement_record.cgst + merchant_settlement_record.igst + merchant_settlement_record.sgst + settlement_entries.amount) as diff,
        "Matching with {{MERCHANT_ID}}" as Reasons,
        1 as filter

FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
INNER JOIN
(Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
 sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(sgst) as sgst, sum(cgst) as cgst, sum(commission) as commission from accounting.settlement_entries AS settlement_entries
	left outer join (SELECT id from valhalla_v2.merchants where merchant_id = '{{MERCHANT_ID}}') q1
	on settlement_entries.to_party_id = q1.id
	where (q1.id is not null)
	AND settlement_entries.transaction_type = 'REVENUE'
	AND settlement_entries.entry_type = 1
	AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
	AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
	group by external_transaction_id
) settlement_entries
ON bbps_transactions.client_reference_id = settlement_entries.external_transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
WHERE ABS(merchant_settlement_record.fee + merchant_settlement_record.cgst + merchant_settlement_record.igst + merchant_settlement_record.sgst + settlement_entries.amount) < 1
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND UPPER(tenant.provider_name) = '{{MERCHANT_ID}}'
group by DATE(merchant_settlement.settlement_date),  DATE(merchant_settlement_record.transaction_date)

UNION

SELECT  DATE(merchant_settlement.settlement_date) as settlement_date,
        DATE(merchant_settlement_record.transaction_date) as transaction_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "REVERSE_REDEMPTION",
        count(merchant_settlement_record.external_transaction_id) as count_of_txn,
        sum(settlement_entries.amount) as total_amount,
        sum(settlement_entries.fee) as fee,
        sum(settlement_entries.igst) as igst,
        sum(settlement_entries.cgst) as cgst,
        sum(settlement_entries.sgst) as sgst,
        sum(settlement_entries.amount) as net_amount,
        sum(merchant_settlement_record.transaction_amount) as amount_as_per_provider,
        sum(merchant_settlement_record.transaction_amount - settlement_entries.amount) as diff,
        "Matching with {{MERCHANT_ID}}" as Reasons,
        1 as filter

FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
INNER JOIN
(Select * from nexus.reversed_transactions as nexus_reversals
where nexus_reversals.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND nexus_reversals.year in ({{YEARS}})
AND nexus_reversals.month in ({{MONTHS}})
and nexus_reversals.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) nexus_reversals
ON bbps_transactions.client_reference_id = nexus_reversals.original_transaction_id
INNER JOIN
(Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
 sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(sgst) as sgst, sum(cgst) as cgst, sum(commission) as commission from accounting.settlement_entries AS settlement_entries
	left outer join (Select id from valhalla_v2.merchants where merchant_id = '{{MERCHANT_ID}}') q1
	on settlement_entries.from_party_id = q1.id
	where (q1.id is not null)
	AND settlement_entries.year in ({{YEARS}})
    AND settlement_entries.month in ({{MONTHS}})
	and settlement_entries.transaction_type = 'REDEMPTION_REVERSE'
	and settlement_entries.entry_type = 0
	and settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
	and settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
	group by external_transaction_id
) settlement_entries
ON nexus_reversals.reversal_id = settlement_entries.external_transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
WHERE ABS(merchant_settlement_record.transaction_amount - settlement_entries.amount) < 1
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND UPPER(tenant.provider_name) = '{{MERCHANT_ID}}'
group by DATE(merchant_settlement.settlement_date),  DATE(merchant_settlement_record.transaction_date)

UNION

SELECT  DATE(merchant_settlement.settlement_date) as settlement_date,
        DATE(merchant_settlement_record.transaction_date) as transaction_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "REVERSE_REVENUE",
        count(merchant_settlement_record.external_transaction_id) as count_of_txn,
        sum(settlement_entries.amount) as total_amount,
        sum(settlement_entries.fee) as fee,
        sum(settlement_entries.igst) as igst,
        sum(settlement_entries.cgst) as cgst,
        sum(settlement_entries.sgst) as sgst,
        sum(settlement_entries.amount) as net_amount,
        sum(merchant_settlement_record.fee + merchant_settlement_record.cgst + merchant_settlement_record.igst + merchant_settlement_record.sgst) as amount_as_per_provider,
        sum(merchant_settlement_record.fee + merchant_settlement_record.cgst + merchant_settlement_record.igst + merchant_settlement_record.sgst + settlement_entries.amount) as diff,
        "Mismatch with {{MERCHANT_ID}}" as Reasons,
        2 as filter

FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
INNER JOIN
(Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
 sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(sgst) as sgst, sum(cgst) as cgst, sum(commission) as commission from accounting.settlement_entries AS settlement_entries
	left outer join (SELECT id from valhalla_v2.merchants where merchant_id = '{{MERCHANT_ID}}') q1
	on settlement_entries.to_party_id = q1.id
	where (q1.id is not null)
	AND settlement_entries.year in ({{YEARS}})
    AND settlement_entries.month in ({{MONTHS}})
	AND settlement_entries.transaction_type = 'REVENUE'
	AND settlement_entries.entry_type = 1
	AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
	AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
	group by external_transaction_id
) settlement_entries
ON bbps_transactions.client_reference_id = settlement_entries.external_transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
WHERE ABS(merchant_settlement_record.fee + merchant_settlement_record.cgst + merchant_settlement_record.igst + merchant_settlement_record.sgst + settlement_entries.amount) >= 1
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND UPPER(tenant.provider_name) = '{{MERCHANT_ID}}'
group by DATE(merchant_settlement.settlement_date),  DATE(merchant_settlement_record.transaction_date)

UNION

SELECT  DATE(merchant_settlement.settlement_date) as settlement_date,
        DATE(merchant_settlement_record.transaction_date) as transaction_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "REVERSE_REDEMPTION",
        count(merchant_settlement_record.external_transaction_id) as count_of_txn,
        sum(settlement_entries.amount) as total_amount,
        sum(settlement_entries.fee) as fee,
        sum(settlement_entries.igst) as igst,
        sum(settlement_entries.cgst) as cgst,
        sum(settlement_entries.sgst) as sgst,
        sum(settlement_entries.amount) as net_amount,
        sum(merchant_settlement_record.transaction_amount) as amount_as_per_provider,
        sum(merchant_settlement_record.transaction_amount - settlement_entries.amount) as diff,
        "Mismatch with {{MERCHANT_ID}}" as Reasons,
        2 as filter

FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
INNER JOIN
(Select * from nexus.reversed_transactions as nexus_reversals
where nexus_reversals.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND nexus_reversals.year in ({{YEARS}})
AND nexus_reversals.month in ({{MONTHS}})
and nexus_reversals.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) nexus_reversals
ON bbps_transactions.client_reference_id = nexus_reversals.original_transaction_id
INNER JOIN
(Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
 sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(sgst) as sgst, sum(cgst) as cgst, sum(commission) as commission from accounting.settlement_entries AS settlement_entries
	left outer join (Select id from valhalla_v2.merchants where merchant_id = '{{MERCHANT_ID}}') q1
	on settlement_entries.from_party_id = q1.id
	where (q1.id is not null)
	AND settlement_entries.year in ({{YEARS}})
    AND settlement_entries.month in ({{MONTHS}})
	and settlement_entries.transaction_type = 'REDEMPTION_REVERSE'
	and settlement_entries.entry_type = 0
	and settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
	and settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
	group by external_transaction_id
) settlement_entries
ON nexus_reversals.reversal_id = settlement_entries.external_transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
WHERE ABS(merchant_settlement_record.transaction_amount - settlement_entries.amount) >= 1
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND UPPER(tenant.provider_name) = '{{MERCHANT_ID}}'
group by DATE(merchant_settlement.settlement_date),  DATE(merchant_settlement_record.transaction_date)

UNION

SELECT  DATE(merchant_settlement.settlement_date) as settlement_date,
        DATE(merchant_settlement_record.transaction_date) as transaction_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "REVERSE_REVENUE",
        count(merchant_settlement_record.external_transaction_id) as count_of_txn,
        0 as total_amount,
        0 as fee,
        0 as igst,
        0 as cgst,
        0 as sgst,
        0 as net_amount,
        sum(merchant_settlement_record.fee + merchant_settlement_record.cgst + merchant_settlement_record.igst + merchant_settlement_record.sgst) as amount_as_per_provider,
        sum(merchant_settlement_record.fee + merchant_settlement_record.cgst + merchant_settlement_record.igst + merchant_settlement_record.sgst) as diff,
        "Missing in Accounting" as Reasons,
        3 as filter

FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
LEFT JOIN
(Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(sgst) as sgst, sum(cgst) as cgst, sum(commission) as commission from accounting.settlement_entries AS settlement_entries
	left outer join (SELECT id from valhalla_v2.merchants where merchant_id = '{{MERCHANT_ID}}') q1
	on settlement_entries.to_party_id = q1.id
	where (q1.id is not null)
	AND settlement_entries.year in ({{YEARS}})
    AND settlement_entries.month in ({{MONTHS}})
	AND settlement_entries.transaction_type = 'REVENUE'
	AND settlement_entries.entry_type = 1
	AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
	AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
	group by external_transaction_id
) settlement_entries
ON bbps_transactions.client_reference_id = settlement_entries.external_transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
where settlement_entries.external_transaction_id IS NULL
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type IN (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND UPPER(tenant.provider_name) = '{{MERCHANT_ID}}'
group by DATE(merchant_settlement.settlement_date),  DATE(merchant_settlement_record.transaction_date)

UNION

SELECT  DATE(merchant_settlement.settlement_date) as settlement_date,
        DATE(merchant_settlement_record.transaction_date) as transaction_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "REVERSE_REVENUE",
        count(settlement_entries.external_transaction_id) as count_of_txn,
        sum(settlement_entries.amount) as total_amount,
        sum(settlement_entries.fee) as fee,
        sum(settlement_entries.igst) as igst,
        sum(settlement_entries.cgst) as cgst,
        sum(settlement_entries.sgst) as sgst,
        sum(settlement_entries.amount) as net_amount,
        0 as amount_as_per_provider,
        sum(settlement_entries.amount) as diff,
        "Missing in MIS report" as Reasons,
        3 as filter

FROM (Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
sum(amount) as amount, sum(fee) as fee, sum(commission) as commission, sum(igst) as igst, sum(cgst) as cgst, sum(sgst) as sgst from accounting.settlement_entries AS settlement_entries
LEFT JOIN (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
WHERE settlement_entries.transaction_type = 'REVENUE'
AND settlement_entries.year in ({{YEARS}})
AND settlement_entries.month in ({{MONTHS}})
AND settlement_entries.entry_type = 1
AND q1.id is not null
AND settlement_entries.transaction_date >= '{{START_DATE}}'
AND settlement_entries.transaction_date < '{{END_DATE}}'
group by external_transaction_id) settlement_entries
INNER JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON settlement_entries.external_transaction_id = bbps_transactions.client_reference_id
LEFT JOIN
(Select * from torch.merchant_settlement_record as merchant_settlement_record
where merchant_settlement_record.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_type in (1,2)
) merchant_settlement_record
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
WHERE (merchant_settlement_record.transaction_id IS NULL)
group by DATE(merchant_settlement.settlement_date),  DATE(merchant_settlement_record.transaction_date)

UNION

SELECT  DATE(merchant_settlement.settlement_date) as settlement_date,
        DATE(merchant_settlement_record.transaction_date) as transaction_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "REVERSE_REDEMPTION",
        count(merchant_settlement_record.external_transaction_id) as count_of_txn,
        0 as total_amount,
        0 as fee,
        0 as igst,
        0 as cgst,
        0 as sgst,
        0 as net_amount,
        sum(merchant_settlement_record.transaction_amount) as amount_as_per_provider,
        sum(merchant_settlement_record.transaction_amount ) as diff,
        "Missing in Accounting" as Reasons,
        3 as filter
FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
INNER JOIN
(Select * from nexus.reversed_transactions as nexus_reversals
 where nexus_reversals.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND nexus_reversals.year in ({{YEARS}})
AND nexus_reversals.month in ({{MONTHS}})
and nexus_reversals.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) nexus_reversals
ON bbps_transactions.client_reference_id = nexus_reversals.original_transaction_id
LEFT JOIN
(Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(sgst) as sgst, sum(cgst) as cgst, sum(commission) as commission from accounting.settlement_entries AS settlement_entries
	left outer join (SELECT id from valhalla_v2.merchants where merchant_id = '{{MERCHANT_ID}}') q1
	on settlement_entries.from_party_id = q1.id
	where (q1.id is not null)
	AND settlement_entries.year in ({{YEARS}})
    AND settlement_entries.month in ({{MONTHS}})
	AND settlement_entries.transaction_type = 'REDEMPTION_REVERSE'
	AND settlement_entries.entry_type = 0
	AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
	AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
	group by external_transaction_id
) settlement_entries
ON nexus_reversals.reversal_id = settlement_entries.external_transaction_id
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
where settlement_entries.external_transaction_id IS NULL
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type in (1,2)
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND UPPER(tenant.provider_name) = '{{MERCHANT_ID}}'
group by DATE(merchant_settlement.settlement_date),  DATE(merchant_settlement_record.transaction_date)

UNION

SELECT  DATE(merchant_settlement.settlement_date) as settlement_date,
        DATE(merchant_settlement_record.transaction_date) as transaction_date,
        '{{MERCHANT_ID}}' as merchant_id,
        "REVERSE_REDEMPTION",
        count(settlement_entries.external_transaction_id) as count_of_txn,
        sum(settlement_entries.amount) as total_amount,
        sum(settlement_entries.fee) as fee,
        sum(settlement_entries.igst) as igst,
        sum(settlement_entries.cgst) as cgst,
        sum(settlement_entries.sgst) as sgst,
        sum(settlement_entries.amount) as net_amount,
        0 as amount_as_per_provider,
        sum(settlement_entries.amount) as diff,
        "Missing in MIS report" as Reasons,
        3 as filter
FROM (Select external_transaction_id, max(created) as created, max(transaction_date) as transaction_date, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable,
sum(amount) as amount, sum(fee) as fee, sum(commission) as commission, sum(igst) as igst, sum(cgst) as cgst, sum(sgst) as sgst from accounting.settlement_entries AS settlement_entries
LEFT JOIN (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.from_party_id = q1.id
WHERE settlement_entries.transaction_type = 'REDEMPTION_REVERSE'
AND settlement_entries.year in ({{YEARS}})
AND settlement_entries.month in ({{MONTHS}})
AND settlement_entries.entry_type = 0
AND q1.id is not null
AND settlement_entries.transaction_date >= '{{START_DATE}}'
AND settlement_entries.transaction_date < '{{END_DATE}}'
group by external_transaction_id) settlement_entries
INNER JOIN
(Select * from nexus.reversed_transactions as nexus_reversals
 where nexus_reversals.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND nexus_reversals.year in ({{YEARS}})
AND nexus_reversals.month in ({{MONTHS}})
and nexus_reversals.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) nexus_reversals
ON settlement_entries.external_transaction_id = nexus_reversals.reversal_id
INNER JOIN
(Select * from bbps.transactions as bbps_transactions
where bbps_transactions.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND bbps_transactions.year in ({{YEARS}})
AND bbps_transactions.month in ({{MONTHS}})
and bbps_transactions.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) bbps_transactions
ON bbps_transactions.client_reference_id = nexus_reversals.original_transaction_id
LEFT JOIN
(Select * from torch.merchant_settlement_record as merchant_settlement_record
where merchant_settlement_record.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
and merchant_settlement_record.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND merchant_settlement_record.status != '24'
and merchant_settlement_record.transaction_type in (1,2)
) merchant_settlement_record
ON merchant_settlement_record.transaction_id = bbps_transactions.transaction_id
WHERE merchant_settlement_record.transaction_id IS NULL
group by DATE(merchant_settlement.settlement_date),  DATE(merchant_settlement_record.transaction_date)
