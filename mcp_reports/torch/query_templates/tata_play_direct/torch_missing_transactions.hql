SELECT nexus_provider_reference_mappings.reference_id as nexusId,merchant_settlement_record.external_transaction_id as mis_external_id, "" as settlement_external_id, "FORWARD" AS transaction_type, "Missing in NEXUS - Present in MIS" AS reason, 0 as settlement_total_payable, merchant_settlement_record.transaction_amount as mis_amount, merchant_settlement_record.transaction_date as mis_date,merchant_settlement_record.transaction_date as settlement_date
FROM torch.merchant_settlement_record AS merchant_settlement_record
LEFT JOIN
(Select * from nexus.provider_reference_mappings as nexus_provider_reference_mappings
where nexus_provider_reference_mappings.year in ({{YEARS}})
AND nexus_provider_reference_mappings.month in ({{MONTHS}}) and nexus_provider_reference_mappings.service_provider_id = 'TATASKYDIRECT' and nexus_provider_reference_mappings.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
and nexus_provider_reference_mappings.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) nexus_provider_reference_mappings
ON merchant_settlement_record.external_transaction_id = nexus_provider_reference_mappings.provider_reference
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
where (nexus_provider_reference_mappings.provider_reference IS NULL OR nexus_provider_reference_mappings.reference_id is NULL)
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type = 0
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND UPPER(tenant.provider_name) = '{{MERCHANT_ID}}'


UNION

SELECT nexus_provider_reference_mappings.reference_id as nexusId,merchant_settlement_record.external_transaction_id as mis_external_id, settlement_entries.external_transaction_id as settlement_external_id, "FORWARD" AS transaction_type, "Missing in accounting" AS reason, settlement_entries.total_payable as settlement_total_payable, merchant_settlement_record.transaction_amount as mis_amount,merchant_settlement_record.transaction_date as mis_date,settlement_entries.transaction_date as settlement_date
FROM torch.merchant_settlement_record AS merchant_settlement_record
INNER JOIN
(Select * from nexus.provider_reference_mappings as nexus_provider_reference_mappings
where nexus_provider_reference_mappings.year in ({{YEARS}})
AND nexus_provider_reference_mappings.month in ({{MONTHS}}) and nexus_provider_reference_mappings.service_provider_id = 'TATASKYDIRECT' and nexus_provider_reference_mappings.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
and nexus_provider_reference_mappings.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) nexus_provider_reference_mappings
ON merchant_settlement_record.external_transaction_id = nexus_provider_reference_mappings.provider_reference
LEFT JOIN

(Select max(created) as created, max(transaction_date) as transaction_date, external_transaction_id, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable, sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(cgst) as cgst, sum(sgst) as sgst from accounting.settlement_entries
left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
on settlement_entries.from_party_id = q2.id
where (q1.id is not null OR q2.id is not null)
AND settlement_entries.year in ({{YEARS}})
AND settlement_entries.month in ({{MONTHS}})
AND settlement_entries.transaction_type= 'REDEMPTION'
AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND settlement_entries.event_type != 'MERCHANT_FULFILMENT_REVERSAL'
group by external_transaction_id) settlement_entries
ON nexus_provider_reference_mappings.reference_id = settlement_entries.external_transaction_id

INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
where settlement_entries.external_transaction_id IS NULL
AND merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_type = 0
AND merchant_settlement_record.status != '24'
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
AND tenant.provider_name = '{{MERCHANT_ID}}'


UNION

SELECT nexus_provider_reference_mappings.reference_id as nexusId,"" as mis_external_id, settlement_entries.external_transaction_id as settlement_external_id, "FORWARD" as transaction_type, "Missing in NEXUS - Present in Accounting" as reason, settlement_entries.total_payable as settlement_total_payable,0 as mis_amount, settlement_entries.transaction_date as mis_date,settlement_entries.transaction_date as settlement_date
FROM
(Select max(created) as created, max(transaction_date) as transaction_date, external_transaction_id, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable, sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(cgst) as cgst, sum(sgst) as sgst from accounting.settlement_entries
left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
on settlement_entries.from_party_id = q2.id
where (q1.id is not null OR q2.id is not null)
AND settlement_entries.year in ({{YEARS}})
AND settlement_entries.month in ({{MONTHS}})
AND settlement_entries.transaction_type= 'REDEMPTION'
AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND settlement_entries.event_type != 'MERCHANT_FULFILMENT_REVERSAL'
group by external_transaction_id) settlement_entries
LEFT JOIN
(Select * from nexus.provider_reference_mappings as nexus_provider_reference_mappings
where nexus_provider_reference_mappings.year in ({{YEARS}})
AND nexus_provider_reference_mappings.month in ({{MONTHS}}) and nexus_provider_reference_mappings.service_provider_id = 'TATASKYDIRECT' and nexus_provider_reference_mappings.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
and nexus_provider_reference_mappings.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) nexus_provider_reference_mappings
ON settlement_entries.external_transaction_id = nexus_provider_reference_mappings.reference_id
where (nexus_provider_reference_mappings.provider_reference IS NULL OR nexus_provider_reference_mappings.reference_id IS NULL)


UNION

SELECT nexus_provider_reference_mappings.reference_id as nexusId,merchant_settlement_record.external_transaction_id as mis_external_id, settlement_entries.external_transaction_id as settlement_external_id, "FORWARD" as transaction_type, "Missing in MIS" as reason, settlement_entries.total_payable as settlement_total_payable,merchant_settlement_record.transaction_amount as mis_amount, merchant_settlement_record.transaction_date as mis_date,settlement_entries.transaction_date as settlement_date
FROM (Select max(created) as created, max(transaction_date) as transaction_date, external_transaction_id, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable, sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(cgst) as cgst, sum(sgst) as sgst from accounting.settlement_entries
left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q1
on settlement_entries.to_party_id = q1.id
left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = '{{MERCHANT_ID}}') q2
on settlement_entries.from_party_id = q2.id
where (q1.id is not null OR q2.id is not null)
AND settlement_entries.year in ({{YEARS}})
AND settlement_entries.month in ({{MONTHS}})
AND settlement_entries.transaction_type= 'REDEMPTION'
AND settlement_entries.transaction_date >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
AND settlement_entries.transaction_date < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
AND settlement_entries.event_type != 'MERCHANT_FULFILMENT_REVERSAL'
group by external_transaction_id) settlement_entries
INNER JOIN
(Select * from nexus.provider_reference_mappings as nexus_provider_reference_mappings
where nexus_provider_reference_mappings.year in ({{YEARS}})
AND nexus_provider_reference_mappings.month in ({{MONTHS}}) and  nexus_provider_reference_mappings.service_provider_id = 'TATASKYDIRECT' and nexus_provider_reference_mappings.created_at >= DATE_ADD('{{START_DATE}}', {{BEFORE_DAY_RANGE}})
and nexus_provider_reference_mappings.created_at < DATE_ADD('{{END_DATE}}', {{AFTER_DAY_RANGE}})
) nexus_provider_reference_mappings
ON settlement_entries.external_transaction_id = nexus_provider_reference_mappings.reference_id
LEFT JOIN
(Select * from torch.merchant_settlement_record as merchant_settlement_record
where merchant_settlement_record.year in ({{YEARS}})
AND merchant_settlement_record.month in ({{MONTHS}})
AND merchant_settlement_record.transaction_date >= '{{START_DATE}}'
AND merchant_settlement_record.transaction_date < '{{END_DATE}}'
and merchant_settlement_record.transaction_type = 0
AND merchant_settlement_record.status != '24'
) merchant_settlement_record
ON merchant_settlement_record.external_transaction_id = nexus_provider_reference_mappings.provider_reference
INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id
INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id
WHERE merchant_settlement_record.external_transaction_id IS NULL AND tenant.provider_name = '{{MERCHANT_ID}}'
