from airflow.operators.python_operator import PythonOperator

from mcp_reports.torch.tasks import execute_hive_query, test_execute_hive_query, generate_output_report, \
    email_generated_report, upload_to_docstore, trigger_sftp_file_fetch, email_sftp_file_fetch_ack, \
    remove_files, clean_up_all_tmp_files, update_match_transactions, update_miss_match_transactions, \
    update_missing_transactions


def execute_hive_query_operator(dag, report_config):
    return PythonOperator(
        task_id='execute_hive_query',
        provide_context=True,
        python_callable=execute_hive_query,
        op_kwargs={
            'report_config': report_config,
        },
        dag=dag,
    )


def test_execute_hive_query_operator(dag, report_config):
    return PythonOperator(
        task_id='execute_hive_query',
        provide_context=True,
        python_callable=test_execute_hive_query,
        op_kwargs={
            'report_config': report_config,
        },
        dag=dag,
    )


def generate_output_report_operator(dag, report_config):
    return PythonOperator(
        task_id='generate_output_report',
        provide_context=True,
        python_callable=generate_output_report,
        op_kwargs={
            'report_config': report_config,
        },
        dag=dag,
    )

def update_match_transactions_operator(dag, report_config):
    return PythonOperator(
        task_id='update_match_transactions',
        provide_context=True,
        python_callable=update_match_transactions,
        op_kwargs={
            'report_config': report_config,
        },
        dag=dag,
    )

def update_miss_match_transactions_operator(dag, report_config):
    return PythonOperator(
        task_id='update_miss_match_transactions',
        provide_context=True,
        python_callable=update_miss_match_transactions,
        op_kwargs={
            'report_config': report_config,
        },
        dag=dag,
    )

def update_missing_transactions_operator(dag, report_config):
    return PythonOperator(
        task_id='update_missing_transactions',
        provide_context=True,
        python_callable=update_missing_transactions,
        op_kwargs={
            'report_config': report_config,
        },
        dag=dag,
    )





def upload_to_docstore_operator(dag, report_config):
    return PythonOperator(
        task_id='upload_to_docstore',
        provide_context=True,
        python_callable=upload_to_docstore,
        op_kwargs={
            'report_config': report_config,
        },
        dag=dag,
    )

def email_generated_report_operator(dag, report_config):
    return PythonOperator(
        task_id='email_generated_report',
        provide_context=True,
        python_callable=email_generated_report,
        op_kwargs={
            'report_config': report_config,
        },
        dag=dag,
    )


def trigger_sftp_file_fetch_operator(dag, sftp_config):
    return PythonOperator(
        task_id='trigger_sftp_file_fetch',
        provide_context=True,
        python_callable=trigger_sftp_file_fetch,
        op_kwargs={
            'sftp_config': sftp_config,
        },
        dag=dag,
    )


def email_sftp_file_fetch_ack_operator(dag, sftp_config):
    return PythonOperator(
        task_id='email_sftp_file_fetch_ack',
        provide_context=True,
        python_callable=email_sftp_file_fetch_ack,
        op_kwargs={
            'sftp_config': sftp_config,
        },
        dag=dag,
    )

def delete_tenant_file_operator(dag, cleanup_config):
    return PythonOperator(
        task_id="delete_tenant_hdfs_file",
        python_callable=remove_files,
        op_kwargs={
            'report_config': cleanup_config,
        },
        provide_context=True,
        dag=dag
    )

def clean_up_all_tmp_files_operator(dag, report_config):
    return PythonOperator(
        task_id='clean_up_all_tmp_files',
        provide_context=True,
        python_callable=clean_up_all_tmp_files,
        op_kwargs={
            'report_config': report_config,
        },
        trigger_rule='all_done',
        dag=dag,
    )