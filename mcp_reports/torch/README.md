## Switch reports
#### Notes
- Always keep `seconds` to `0` in cron schedules of reports in `main.py`
- All reports with same `schedules[].frequency` and `schedules[].batchId` will be triggered in sequence (arbitrary).
- Supported query sources are `hive`.
- Make sure that columns are coherent with output of query.
- The following template variables will be replaced in query using [pybars](https://pypi.org/project/pybars/):
  - YEARS (`,` separated list of years in the range)
  - MONTHS (`,` separated list of months in the range)
  - For X in START, END, EXECUTION\*
      - X_DATE (`%Y-%m-%d` format of x date)
      - X_TIME (`pendulum.Pendulum` object of x time which is by default formatted as `2019-04-01T00:00:00+05:30`)
      - X_DAY (day of the month of x date)
      - X_MONTH (month of x date)
      - X_YEAR (year of x date)


#### Meta Notes
- \* The following stunts are performed by highly trained professionals under expert supervision, plz don't try this at home or anywhere else.
