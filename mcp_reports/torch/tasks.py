import csv
import json
import logging
import traceback
import uuid
import pendulum
import requests
from hive.hook.secured_hive_cli_hook import SecuredH<PERSON><PERSON><PERSON>Hook
from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter, Retry
from requests_toolbelt.multipart.encoder import MultipartEncoder

from mcp_reports.torch.config.config import HDFS_USER_ID, TEMP_TSV_HDFS_PATH, TEMP_TSV_LOCAL_PATH, \
    TEMP_CSV_LOCAL_PATH, REALM, TEMP_CSV_HDFS_PATH, OLYMPUS_PROTOCOL, OLYMPUS_HOST, OLYMPUS_PORT, \
    OLYMPUS_CLIENT_ID, OLYMPUS_CLIENT_KEY, TEST_QUERY_CONFIG, HDFS_TENANT_CONFIG_PATH
from mcp_reports.torch.config.config import TORCH_SERVICE_URL
from mcp_reports.torch.olympus_im_client import OlympusIMClient
from mcp_reports.torch.utils.common import read_query_template, format_query, get_execution_date_range, \
    get_formatted_date, \
    write_column_headers_to_file, apply_template, read_email_template, \
    remove_file_pattern, determine_execution_date, get_day_range, is_query_modifyable, get_hive_queue_name, \
    touch_file_name, \
    get_header_file_name, get_query_file_name, get_final_report_file_name, isBlank, input_date_fall_in_between, \
    is_match_upload_docstore_disable, get_report_category, get_report_type, get_tenant_query_template
from mcp_reports.torch.utils.hdfs import delete_from_hdfs, create_hdfs_dir, write_hive_hdfs_output_to_csv_file, \
    print_file_data, \
    merge_in_hdfs_dir_from_header_and_query_data, print_file_data_final, list_dir_files, get_hdfs_stream, \
    concat_local_to_hdfs_dir
from mcp_reports.torch.zencast_client import ZencastClient


def get_file_paths(report_name, file_name, start_date, end_date, execution_date, merchant_id, query):
    query = query.replace("/", "_") if query is not None else query
    temp_tsv_local_path = TEMP_TSV_LOCAL_PATH.format(REPORT_NAME=report_name,
                                                     START_DATE=get_formatted_date(start_date),
                                                     END_DATE=get_formatted_date(end_date),
                                                     EXECUTION_DATE=get_formatted_date(execution_date),
                                                     QUERY=query)
    temp_tsv_hdfs_path = TEMP_TSV_HDFS_PATH.format(REPORT_NAME=report_name,
                                                   START_DATE=get_formatted_date(start_date),
                                                   END_DATE=get_formatted_date(end_date),
                                                   EXECUTION_DATE=get_formatted_date(execution_date),
                                                   QUERY=query)

    temp_csv_hdfs_path = TEMP_CSV_HDFS_PATH.format(REPORT_NAME=report_name,
                                                   START_DATE=get_formatted_date(start_date),
                                                   END_DATE=get_formatted_date(end_date),
                                                   EXECUTION_DATE=get_formatted_date(execution_date))
    temp_csv_local_path = TEMP_CSV_LOCAL_PATH + file_name.format(START_DATE=get_formatted_date(start_date),
                                                                 END_DATE=get_formatted_date(end_date),
                                                                 MERCHANT_ID=merchant_id)
    temp_csv_local_path_email = TEMP_CSV_LOCAL_PATH + "TORCH_" + file_name.format(
        START_DATE=get_formatted_date(start_date),
        END_DATE=get_formatted_date(end_date),
        MERCHANT_ID=merchant_id)
    temp_hdfs_output_dir = temp_tsv_hdfs_path[:-4]
    temp_csv_hdfs_dir = temp_csv_hdfs_path[:-4]

    return temp_tsv_local_path, temp_tsv_hdfs_path, temp_hdfs_output_dir, temp_csv_local_path, temp_csv_hdfs_dir, temp_csv_local_path_email


def execute_hive_query(report_config, **kwargs):
    execution_date = determine_execution_date(**kwargs)
    start_date, end_date = get_execution_date_range(execution_date, report_config)
    start_day_range, end_day_range = get_day_range(report_config)
    merchant_id = report_config['merchantId']

    for template_path in report_config['query']['templatePath']:
        logging.info("report_config config is  ({})".format(report_config))
        query_template = get_tenant_query_template(report_config)
        logging.info("query_template config is  ({})".format(query_template))
        if query_template == "":
            query_template = read_query_template(template_path)
        _, _, temp_hdfs_output_dir, _, _, _ = get_file_paths(report_config['name'], report_config['fileName'],
                                                             start_date, end_date, execution_date, merchant_id,
                                                             template_path)

        hql = format_query(query_template, start_date, end_date, execution_date, merchant_id, start_day_range,
                           end_day_range,report_config)


        hiveConfigDefault = { 'hive.exec.compress.output': 'false','hive.merge.tezfiles': 'true'}
        hiveConfigDefault['tez.queue.name']=get_hive_queue_name(report_config)
        hiveConfig = {}
        for key in hiveConfigDefault.keys():
            hiveConfig[key] = hiveConfigDefault[key]

        if 'hiveConfig' in report_config:
            for key in report_config['hiveConfig'].keys():
                hiveConfig[key] = report_config['hiveConfig'][key]

        config = []
        for key in hiveConfig.keys():
            config.append("""set {configKey} = {configValue};""".format(configKey=key, configValue=hiveConfig[key]))

        hiveConfigStr = '\n'.join(config)
        hql = """{hiveConfigStr}
        INSERT OVERWRITE DIRECTORY '{report_directory}' ROW FORMAT DELIMITED FIELDS TERMINATED BY ',' LINES TERMINATED BY '\n' NULL DEFINED AS ' ' STORED AS TEXTFILE
        """.format(hiveConfigStr=hiveConfigStr,report_directory=temp_hdfs_output_dir) + hql
        hive_hook = SecuredHiveCliHook(user=HDFS_USER_ID, realm=REALM)

        logging.info("Hql query ({})".format(hql))

        create_hdfs_dir(temp_hdfs_output_dir)
        kwargs['ti'].xcom_push("temp_csv_hdfs_dir", temp_hdfs_output_dir)
        hive_hook.run_cli(hql)

def test_execute_hive_query(report_config,**kwargs):
    logging.info("Report config is  ({})".format(report_config))
    is_in_valid_query = is_query_modifyable(report_config)
    if is_in_valid_query:
        print("Modifiable query is configured...")
        return
    execution_date = determine_execution_date(**kwargs)
    query_temp = report_config['query']['test_query'];

    query_temp = query_temp.replace('{', '{{')
    query_temp = query_temp.replace('}', '}}')

    start_date, end_date = get_execution_date_range(execution_date, report_config)
    start_day_range, end_day_range = get_day_range(report_config)
    merchant_id = report_config['merchantId']
    test_hive_query(end_date, end_day_range, execution_date, kwargs, merchant_id, report_config, start_date,start_day_range, query_temp)


def test_hive_query(end_date, end_day_range, execution_date, kwargs, merchant_id, report_config, start_date,
                    start_day_range, query_temp):
        _, _, temp_hdfs_output_dir, _, _, _ = get_file_paths(report_config['name'], report_config['fileName'],
                                                             start_date, end_date, execution_date, merchant_id,
                                                             report_config['name'])

        hql = format_query(query_temp, start_date, end_date, execution_date, merchant_id, start_day_range,
                           end_day_range,report_config)


        hiveConfigDefault = { 'hive.exec.compress.output': 'false','hive.merge.tezfiles': 'true'}
        hiveConfigDefault['tez.queue.name']=get_hive_queue_name(report_config)
        hiveConfig = {}
        for key in hiveConfigDefault.keys():
            hiveConfig[key] = hiveConfigDefault[key]

        if 'hiveConfig' in report_config:
            for key in report_config['hiveConfig'].keys():
                hiveConfig[key] = report_config['hiveConfig'][key]

        config = []
        for key in hiveConfig.keys():
            config.append("""set {configKey} = {configValue};""".format(configKey=key, configValue=hiveConfig[key]))

        hiveConfigStr = '\n'.join(config)
        hql = """{hiveConfigStr}
        INSERT OVERWRITE DIRECTORY '{report_directory}' ROW FORMAT DELIMITED FIELDS TERMINATED BY ',' LINES TERMINATED BY '\n' NULL DEFINED AS ' ' STORED AS TEXTFILE
        """.format(hiveConfigStr=hiveConfigStr,report_directory=temp_hdfs_output_dir) + hql
        hive_hook = SecuredHiveCliHook(user=HDFS_USER_ID, realm=REALM)

        logging.info("Hql query ({})".format(hql))

        create_hdfs_dir(temp_hdfs_output_dir)
        kwargs['ti'].xcom_push("temp_csv_hdfs_dir", temp_hdfs_output_dir)
        hive_hook.run_cli(hql)



def generate_output_report(report_config, **kwargs):
    execution_date = determine_execution_date(**kwargs)
    start_date, end_date = get_execution_date_range(execution_date, report_config)
    columns = ",".join(report_config['columns'])
    columns = columns.format(MERCHANT_ID=report_config.get('merchantId'))
    if report_config['fileType'] == '.psv':
        columns = "|".join(report_config['columns'])

    for template_path in report_config['query']['templatePath']:
        temp_hdfs_output_dir = get_hdfs_output_dir(end_date, execution_date, report_config, start_date, template_path)
        header_file_name = get_header_file_name(end_date, execution_date, report_config, start_date)
        query_file_name = get_query_file_name(end_date, execution_date, report_config, start_date)

        try:
            write_hive_hdfs_output_to_csv_file(temp_hdfs_output_dir, query_file_name)
            print_file_data(temp_hdfs_output_dir,query_file_name)
            touch_file_name(header_file_name)
            write_column_headers_to_file(header_file_name, columns)
            concat_local_to_hdfs_dir(temp_hdfs_output_dir,header_file_name)
            print_file_data(temp_hdfs_output_dir, query_file_name)
            merge_in_hdfs_dir_from_header_and_query_data(temp_hdfs_output_dir, header_file_name, query_file_name)
            print_file_data_final(temp_hdfs_output_dir, query_file_name)
            list_dir_files(temp_hdfs_output_dir)
        except Exception:
            logging.error("Failed to generate output. Exception: %s" % traceback.format_exc())
        remove_file_pattern(header_file_name)

def upload_to_docstore(report_config, **kwargs):
    merchant_id = report_config['merchantId']
    execution_date = determine_execution_date(**kwargs)
    start_date, end_date = get_execution_date_range(execution_date, report_config)
    if is_match_upload_docstore_disable(report_config) :
        logging.info("Invoking the upload docstore disabled for merchant")
        return
    logging.info("Invoking the upload docstore")
    for template_path in report_config['query']['templatePath']:

        temp_hdfs_output_dir = get_hdfs_output_dir(end_date, execution_date, report_config, start_date, template_path)
        header_file_name = get_header_file_name(end_date, execution_date, report_config, start_date)
        query_file_name = get_query_file_name(end_date, execution_date, report_config, start_date)
        final_output_name_file = get_final_report_file_name(query_file_name, temp_hdfs_output_dir)

        file_name = report_config['fileName'].format(
            START_DATE=get_formatted_date(start_date),
            END_DATE=get_formatted_date(end_date), MERCHANT_ID=merchant_id)

        file_name = file_name[:-4] + "_" + get_formatted_date(execution_date) + ".csv"

        headers = get_torch_service_headers()
        headers.pop("Content-Type");

        torch_endpoint = TORCH_SERVICE_URL + "/v1/upload/processed/file"
        reportType =get_report_type(report_config)
        reportCategory =get_report_category(report_config)

        processed_file_upload_request = '{"providerName":"' + merchant_id + '",'+\
                                    '"reportSettlementStartDate":"' + get_formatted_date(start_date) + '",'+\
                                    '"reportSettlementEndDate":"' + get_formatted_date(end_date) + '",'+\
                                    '"reportType":"' + reportType + '",'+\
                                    '"reportCategory":"' + reportCategory + '",'+\
                                    '"format":"ZIP","fileOperation":"CREATE",' \
                                                                          '"fileName":"' + file_name + '" }'

        logging.info("Opening file from the path ({})".format(final_output_name_file))
        logging.info("processed_file_upload_request ({})".format(processed_file_upload_request))

        try:
            logging.info("Uploading processed file to Docstore via Torch")
            m = MultipartEncoder(
                    fields={'processedFileUploadRequest': processed_file_upload_request,
                    'file': ('filename', get_hdfs_stream(final_output_name_file), 'text/plain')}
                    )
            headers["Content-Type"]=m.content_type
            res =requests.post(torch_endpoint,verify=False, data=m,headers=headers)
            logging.info("Response is  ({}) ".format(res))
            logging.info("Response text is  ({}) ".format(res.text))
            json_data = json.loads(res.text)
            logging.info("Pushing fileId({}) and fileName({}) to XCom".format(json_data['id'], file_name))
            kwargs['ti'].xcom_push("file_id", json_data["id"])
            kwargs['ti'].xcom_push("file_name", file_name)

        except Exception:
            logging.error("Failed to upload to docstore. Exception: %s" % traceback.format_exc())

        logging.info("Removing file pattern :({})".format(header_file_name))
        remove_file_pattern(header_file_name)

def update_match_transactions(report_config, **kwargs):
    update_status_transactions("MATCHED", report_config, **kwargs)



def update_miss_match_transactions(report_config, **kwargs):
     update_status_transactions("MIS_MATCHED", report_config, **kwargs)


def update_missing_transactions(report_config, **kwargs):
    update_status_transactions("MISSING", report_config, **kwargs)


def update_status_transactions(status,report_config, **kwargs):
    torch_endpoint = TORCH_SERVICE_URL + "/v1/merchant/settlement/record/update"
    merchant_id = report_config['merchantId']
    execution_date = determine_execution_date(**kwargs)
    start_date, end_date = get_execution_date_range(execution_date, report_config)

    for template_path in report_config['query']['templatePath']:

        temp_hdfs_output_dir = get_hdfs_output_dir(end_date, execution_date, report_config, start_date, template_path)
        query_file_name = get_query_file_name(end_date, execution_date, report_config, start_date)
        final_output_name_file = get_final_report_file_name(query_file_name, temp_hdfs_output_dir)

        logging.info("Opening file from the path for update status ({})".format(final_output_name_file))
        encoding = 'ascii'
        std_out_final = get_hdfs_stream(final_output_name_file);
        output = std_out_final.decode(encoding)
        reader = csv.reader(output.splitlines(), delimiter=",")
        logging.info("After reading one line....")
        next(reader, None)
        batch_size = 500
        lines = list(reader)
        logging.info("Number of rows found are: {} for status {}".format(len(lines), status))
        s = requests.Session()
        retries = Retry(total=5,
                        backoff_factor=10,
                        status_forcelist=[400, 401, 402, 404, 500, 502, 503, 504],
                        method_whitelist=frozenset(['POST']))

        s.mount('https://', HTTPAdapter(max_retries=retries))

        for i in range(0, len(lines), batch_size):
            merchant_settlement_records = []
            input_list = lines[i:i + batch_size]
            for item in input_list:
                merchant_settlement_record = prepare_status_request(item, status, merchant_id,start_date,end_date)
                if merchant_settlement_record:
                    merchant_settlement_records.append(merchant_settlement_record)
            try:
                if len(merchant_settlement_records) == 0:
                    logging.info('the merchant_settlement_records is empty')
                    continue
                headers = get_torch_service_headers()
                logging.info("Calling torch for updating merchant settlement for merchantID :  " + str(merchant_id))
                res = s.post(torch_endpoint, data=json.dumps(merchant_settlement_records), verify=False
                             , headers=headers)
                if res.status_code != 200:
                    logging.error("torch update status failed with error code ({})".format(res.status_code))
                    raise Exception()
            except Exception as e:
                logging.error("torch update status failed with exception {}".format(e))
                raise e


def prepare_status_request(item, status,merchant_id,start_date,end_date):
    if status == 'MISSING':
        external_transaction_id = item[1]
        input_date =  item[7]
    elif status == 'MIS_MATCHED':
        external_transaction_id = item[2]
        input_date =  item[6]
    else:
        external_transaction_id = item[2]
        input_date = item[5]

    if isBlank(external_transaction_id) or isBlank(input_date):
        return

    pd_input_date =pendulum.parse(input_date)
    if input_date_fall_in_between(pd_input_date,start_date,end_date):
        merchant_settlement_record = {
            "providerName": merchant_id,
            "externalTransactionId": external_transaction_id,
            "status": status
        }
        return  merchant_settlement_record
    else:
        logging.info("Input date does not fall into start and end date : "+str(start_date) + " :: "+str(pd_input_date) +" :: "+str(end_date) )




def clean_up_all_tmp_files(report_config, **kwargs):

    execution_date = determine_execution_date(**kwargs)
    start_date, end_date = get_execution_date_range(execution_date, report_config)

    for template_path in report_config['query']['templatePath']:
        temp_hdfs_output_dir = get_hdfs_output_dir(end_date, execution_date, report_config, start_date, template_path)
        logging.info("Cleaning csv hdfs path dir :({})".format(temp_hdfs_output_dir))
        delete_from_hdfs(temp_hdfs_output_dir)

    logging.info("Cleaning from all the tmp location is done for config {}".format(report_config))

def email_generated_report(report_config, **kwargs):
    if not report_config.get('email'):
        logging.info("Skipping email. No email config found.")
        return


    email_config = report_config['email']
    merchant_id = report_config['merchantId']
    execution_date = determine_execution_date(**kwargs)
    start_date, end_date = get_execution_date_range(execution_date, report_config)

    logging.info("Generating requestId for sending email via Zencast")
    request_id = str(uuid.uuid1())

    logging.info("Pulling fileId and fileName from XCom")
    file_id = kwargs['ti'].xcom_pull(task_ids="upload_to_docstore", key="file_id")
    file_name = kwargs['ti'].xcom_pull(task_ids="upload_to_docstore", key="file_name")

    context = {
        'START_DATE': get_formatted_date(start_date),
        'END_DATE': get_formatted_date(end_date),
        'EXECUTION_DATE': get_formatted_date(execution_date),
        'MERCHANT_ID': merchant_id,
        'FILE_ID': file_id,
        'FILE_NAME': file_name
    }
    recipients = email_config['to']
    subject = email_config['subject'].format(**context)
    content = apply_template(read_email_template(
        email_config['templatePath']
    ).encode(), context)

    logging.info("Initializing Zencast client")
    zencast_client = ZencastClient()

    try:
        logging.info(
            "Sending email to({}), subject({}) and requestId({}) with attached file({})".format(
                recipients, subject, request_id, file_id))
        zencast_client.send_email(request_id, recipients, str(content), subject)
    except Exception:
        logging.error("Failed to send E-Mail. Exception: %s" % traceback.format_exc())



def trigger_sftp_file_fetch(sftp_config, **kwargs):
    headers = get_torch_service_headers()
    torch_endpoint = TORCH_SERVICE_URL + "/v1/sftp/fetch"
    execution_date = determine_execution_date(**kwargs)
    settlementDate = execution_date.subtract(days=sftp_config['settlementDateDiff']).end_of('day')
    logging.info("Torch SFTP file for tenant: ({}), providerName ({}), settlementDate ({}) "
                 "settlementType: ({})".format(
        sftp_config['tenantName'], sftp_config['merchantId'], settlementDate,
        sftp_config['settlementType']))

    data = {'providerName': sftp_config['merchantId'], 'tenantName': sftp_config['tenantName'], 'settlementType': sftp_config['settlementType'],
             'settlementDate':  get_formatted_date(settlementDate)}
    try:
        logging.info("Triggering sftp file fetch in Torch")
        logging.basicConfig(level=logging.DEBUG)
        s = requests.Session()
        retries = Retry(total=5,
                        backoff_factor=1,
                        status_forcelist=[400, 401, 402, 404, 500, 502, 503, 504],
                        method_whitelist=frozenset(['POST']))

        s.mount('http://', HTTPAdapter(max_retries=retries))
        res = s.post(torch_endpoint, json=data, verify=False, headers=headers)
        if res.json()["allFilesUploadedSuccessfully"]:
            logging.info("Successfully fetched sftp file/s and uploaded to docstore ({})".format(res.json()))
        else:
            logging.error("Torch SFTP file fetch failed for tenant: ({}), providerName ({}), settlementDate ({}) "
                          "settlementType: ({}) with response ({})".format(
                sftp_config['tenantName'], sftp_config['merchantId'], settlementDate,
                sftp_config['settlementType'], res.json()))
            raise Exception()

    except Exception as e:
        logging.error("Torch SFTP file fetch failed for tenant: ({}), providerName ({}), settlementDate ({}) "
                      "settlementType: ({})".format(
            sftp_config['tenantName'], sftp_config['merchantId'], settlementDate,
            sftp_config['settlementType']))
        logging.error("Failed to fetch and upload to docstore. Exception: %s" % traceback.format_exc())
        raise e


def get_torch_service_headers():
    olympusClient = OlympusIMClient(OLYMPUS_PROTOCOL, OLYMPUS_HOST, OLYMPUS_PORT,
                                    OLYMPUS_CLIENT_ID, OLYMPUS_CLIENT_KEY)
    return {"Content-Type": "application/json", "Accept": "application/json",
               'Authorization': olympusClient.get_bearer_auth_token()}


def email_sftp_file_fetch_ack(sftp_config, **kwargs):
    if not sftp_config.get('email'):
        logging.info("Skipping email. No email config found.")
        return

    email_config = sftp_config['email']
    merchant_id = sftp_config['merchantId']
    execution_date = determine_execution_date(**kwargs)
    settlementDate = execution_date.subtract(days=sftp_config['settlementDateDiff']).end_of('day')

    logging.info("Generating requestId for sending email via Zencast")
    request_id = str(uuid.uuid1())

    context = {
        'SETTLEMENT_DATE': get_formatted_date(settlementDate),
        'EXECUTION_DATE': get_formatted_date(execution_date),
        'MERCHANT_ID': merchant_id,
    }
    recipients = email_config['to']
    subject = email_config['subject'].format(**context)
    content = apply_template(read_email_template(
        email_config['templatePath']
    ).encode(), context)

    logging.info("Initializing Zencast client")
    zencast_client = ZencastClient()

    logging.info(
        "Sending email to({}), subject({}) and requestId({})".format(
            recipients[0], subject, request_id))
    try:
        zencast_client.send_email(request_id, str(recipients[0]), str(content), subject)
    except Exception as e:
        logging.error("Failed to send E-Mail. Exception: %s" % traceback.format_exc())
        raise e

def get_hdfs_file_paths(tenant_name):
    return HDFS_TENANT_CONFIG_PATH.get(tenant_name, {});

def remove_hdfs_files(tenant_file_path):
    logging.info("Removing file from path of tenant {} ".format(tenant_file_path))
    delete_from_hdfs(tenant_file_path)

def remove_files(**kwargs):
    for tenant_name  in HDFS_TENANT_CONFIG_PATH:
        tenant_file_path= get_hdfs_file_paths(tenant_name);
        remove_hdfs_files(tenant_file_path);

def get_hdfs_output_dir(end_date, execution_date, report_config, start_date, template_path):
    temp_tsv_local_path, _, temp_hdfs_output_dir, temp_csv_local_path, temp_csv_hdfs_dir, _ = get_file_paths(
        report_config['name'], report_config['fileName'], start_date, end_date, execution_date,
        report_config['merchantId'], template_path)
    return temp_hdfs_output_dir
