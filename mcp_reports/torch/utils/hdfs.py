import logging
import os

from mcp_reports.torch.config.config import HDFS_USER_ID, HDFS_CMD
from mcp_reports.torch.utils.common import run_cmd, remove_file_pattern_using_airflow_user, \
    chmod_for_user, create_dirs_for_user, run_stream_cmd, get_final_report_file_name, run_stream_as_list_cmd

MKDIR_CMD = """-mkdir -p "{HDFS_DIR_PATH}" """
PUT_CMD = """-put -f "{LOCAL_PATH}" "{HDFS_PATH}" """
GET_CMD = """-get -f "{HDFS_PATH}" "{LOCAL_PATH}" """
RM_CMD = """-rm -r -f "{HDFS_PATH}" """
GETMERGE_CMD = """-getmerge "{HDFS_DIR}" "{LOCAL_PATH}" """
LS_CMD = """-ls /etc/security/keytabs/sync/"""
CAT_CMD = """cat /etc/passwd"""
CONCAT_CMD = """-cat "{HDFS_DIR}" | hadoop fs -put - "{LOCAL_PATH}" """
FILE_EXIST_CMD = """-test -e "{HDFS_DIR}" """

def hdfs_dir_exist(dir_path):
    logging.info("Checking if  hdfs directory: dir_path({}) exist".format(dir_path))
    dir_exist_cmd = FILE_EXIST_CMD.format(HDFS_DIR=dir_path)
    run_hdfs_cmd(dir_exist_cmd)

CONCAT_HEADER_CMD = """-cat "{HDFS_DIR_HEADER}"  "{OUTPUT_PATH}" | hadoop fs -put - "{FINAL_OUTPUT}" """

CONCAT_PRINT_CMD = """-cat "{HDFS_DIR}" | head -10 """
HEADER_APPEND_CMD = """-cat "{LOCAL_PATH}" | head -1 | hadoop -put - "{HDFS_DIR}" """
LS_HDFS_CMD = """-ls "{HDFS_DIR}" """
CAT_OPEN_HDFS_FILE_CMD = """-cat "{HDFS_FILE_PATH}" """

def run_hdfs_cmd(cmd, user_id=HDFS_USER_ID):
    cmd = HDFS_CMD.format(USER_ID=user_id, CMD=cmd)
    logging.info("command is : ({})".format(cmd))
    run_cmd(cmd)


def stdout_return_run_hdfs_cmd(cmd, user_id=HDFS_USER_ID):
    cmd = HDFS_CMD.format(USER_ID=user_id, CMD=cmd)
    logging.info("command is : ({})".format(cmd))
    return run_stream_cmd(cmd)

def stdout_return_list_run_hdfs_cmd(cmd, user_id=HDFS_USER_ID):
    cmd = HDFS_CMD.format(USER_ID=user_id, CMD=cmd)
    logging.info("command is : ({})".format(cmd))
    return run_stream_as_list_cmd(cmd)


def create_hdfs_dir(dir_path):
    create_dir_cmd = MKDIR_CMD.format(HDFS_DIR_PATH=dir_path)
    run_hdfs_cmd(create_dir_cmd)


def delete_from_hdfs(hdfs_path):
    delete_cmd = RM_CMD.format(HDFS_PATH=hdfs_path)
    run_hdfs_cmd(delete_cmd)


def concat_hdfs_dir_to_local(hdfs_dir, local_path):
    dir_path = os.path.dirname(local_path)
    create_dirs_for_user(dir_path)
    getmerge_cmd = GETMERGE_CMD.format(HDFS_DIR=hdfs_dir, LOCAL_PATH=local_path)
    run_hdfs_cmd(getmerge_cmd)
    chmod_for_user(dir_path)


def list_dir_files(hdfs_dir):
    logging.info("Listing the files  of HDFS directory for tenant query")
    output_hdfs_dir = hdfs_dir + "/"
    ls_hdfs_cmd = LS_HDFS_CMD.format(HDFS_DIR=output_hdfs_dir)
    run_hdfs_cmd(ls_hdfs_cmd)

def get_hdfs_stream(hdfs_file_path):
    logging.info("Returning the stream of hdfs path")
    cat_hdfs_cmd = CAT_OPEN_HDFS_FILE_CMD.format(HDFS_FILE_PATH=hdfs_file_path)
    return stdout_return_run_hdfs_cmd(cat_hdfs_cmd)

def get_hdfs_stream(hdfs_file_path):
    logging.info("Returning the stream of hdfs path")
    cat_hdfs_cmd = CAT_OPEN_HDFS_FILE_CMD.format(HDFS_FILE_PATH=hdfs_file_path)
    return stdout_return_run_hdfs_cmd(cat_hdfs_cmd)

def get_hdfs_stream_as_list(hdfs_file_path):
    logging.info("Returning the stream of hdfs path")
    cat_hdfs_cmd = CAT_OPEN_HDFS_FILE_CMD.format(HDFS_FILE_PATH=hdfs_file_path)
    return stdout_return_list_run_hdfs_cmd(cat_hdfs_cmd)

def print_file_data(hdfs_dir,output_file_name):
    logging.info("Printing the content of HDFS file for tenant query")
    output_hdfs_dir = hdfs_dir + "/" + output_file_name
    concat_print_cmd = CONCAT_PRINT_CMD.format(HDFS_DIR=output_hdfs_dir)
    run_hdfs_cmd(concat_print_cmd)

def print_file_data_final(hdfs_dir,output_file_name):
    final_output_name_file = hdfs_dir + "/final_" + output_file_name
    concat_print_cmd = CONCAT_PRINT_CMD.format(HDFS_DIR=final_output_name_file)
    run_hdfs_cmd(concat_print_cmd)


def write_hive_hdfs_output_to_csv_file(hdfs_dir, output_file_name):
    output_hdfs_dir = hdfs_dir + "/" + output_file_name
    all_hdfs_files =hdfs_dir+"/*"
    logging.info("Writting content from hdfs tsv to csv conversion")
    concat_cmd = CONCAT_CMD.format(HDFS_DIR=all_hdfs_files, LOCAL_PATH=output_hdfs_dir)
    run_hdfs_cmd(concat_cmd)

def merge_in_hdfs_dir_from_header_and_query_data(hdfs_dir, header_file_name, output_file_name):
    output_hdfs_dir_name = hdfs_dir + "/" + output_file_name
    header_name_file = hdfs_dir + "/" + header_file_name
    final_output_name_file = get_final_report_file_name(output_file_name, hdfs_dir)
    logging.info("Writting contents from output to header file.....")
    logging.info("Writing content of header({})".format(header_name_file))
    logging.info("Writing content of csv file({}) ".format(output_hdfs_dir_name))
    logging.info("Writing content of final csv file({}) ".format(final_output_name_file))
    concat_cmd = CONCAT_HEADER_CMD.format(HDFS_DIR_HEADER=header_name_file, OUTPUT_PATH=output_hdfs_dir_name, FINAL_OUTPUT=final_output_name_file)
    run_hdfs_cmd(concat_cmd)

def concat_hdfs_dir_to_local_file(hdfs_dir, local_file,output_file_name):
    final_output_name_file = hdfs_dir + "/final_" + output_file_name
    getmerge_cmd = GETMERGE_CMD.format(HDFS_DIR=final_output_name_file, LOCAL_PATH=local_file)
    run_hdfs_cmd(getmerge_cmd)

def remove_crc_for_file_path(file_path):
    logging.info("Removing crc file for file_path({})".format(file_path))
    dir_path = os.path.dirname(file_path)
    file_name = os.path.basename(file_path)
    remove_file_pattern_using_airflow_user("{}/.{}.crc".format(dir_path, file_name))


def concat_local_to_hdfs_dir(hdfs_dir, local_path):
    remove_crc_for_file_path(local_path)
    logging.info("Writing content of local_path({}) to hdfs_dir({})".format(local_path, hdfs_dir))
    create_hdfs_dir(hdfs_dir)
    put_cmd = PUT_CMD.format(LOCAL_PATH=local_path, HDFS_PATH=hdfs_dir)
    run_hdfs_cmd(put_cmd)
