import json
import logging
import os
import subprocess
from datetime import datetime

import pendulum
import imp
from pybars import Compiler
from zipfile import <PERSON>ipFile, ZIP_DEFLATED
from mcp_reports.torch.config.config import REPORT_CONFIG_DIR, \
    QUERY_TEMPLATES_DIR, EMAIL_TEMPLATES_DIR, PYTHON_FILES_DIR, \
    HDFS_USER_ID, DATE_RANGE_CONFIG
from mcp_reports.torch.constants.schedule_constants import START_OF_FIRST_WEEK_DAY, \
    START_OF_SECOND_WEEK_DAY, START_OF_THIRD_WEEK_DAY, START_OF_FOURTH_WEEK_DAY, \
    LAST_MONTH_NUMBER, START_MONTH_NUMBER, MONTH_TILL_DATE_SCHEDULE_TYPE,ADHOC_SCHEDULE_TYPE,MONTHY_SCHEDULE_TYPE


def apply_template(template_data, template_context):
    compiler = Compiler()
    logging.info("Template data with encode is : {}".format(template_data))
    logging.info("Template context with encode is : {}".format(template_context))
    template = compiler.compile(template_data.decode())
    return ''.join(template(template_context))

def get_execution_date_range(execution_date, report_config):
    if get_schedule_type(report_config)==MONTH_TILL_DATE_SCHEDULE_TYPE:
        return get_date_range_for_mtd_schedule(execution_date)
    elif get_schedule_type(report_config)==MONTHY_SCHEDULE_TYPE:
        return get_date_range_of_previous_month(execution_date)
    else:
        return get_date_range_for_adhoc_schedule(execution_date,report_config)

def is_match_upload_docstore_disable(report_config):
    if get_schedule_type(report_config)==MONTHY_SCHEDULE_TYPE :
        return is_doc_store_upload_disabled(report_config)

def get_schedule_type(report_config):
    return DATE_RANGE_CONFIG.get(report_config['name'], {}) \
            .get('SCHEDULE_TYPE', ADHOC_SCHEDULE_TYPE);

def get_tenant_query_template(report_config):
    query_template=  DATE_RANGE_CONFIG.get(report_config['name'], {}) \
            .get('QUERY_TEMPLATE', "");
    query_template = query_template.replace("[[", "{{")
    query_template = query_template.replace("]]", "}}")
    return  query_template

def is_doc_store_upload_disabled(report_config):
    return DATE_RANGE_CONFIG.get(report_config['name'], {}) \
            .get('DOC_UPLOAD_DISABLED', False);

def get_date_range_for_adhoc_schedule(execution_date,report_config):
    days_before_curr_date = DATE_RANGE_CONFIG.get(report_config['name'], {}) \
        .get('DAYS_BEFORE_CURR_DATE', 1)
    start_date = execution_date.subtract(days=days_before_curr_date).end_of('day')

    duration_in_days = DATE_RANGE_CONFIG.get(report_config['name'], {}) \
        .get('DURATION_IN_DAYS', 1)
    end_date = start_date.add(days=duration_in_days).start_of('day')

    return start_date,end_date

def get_date_range_of_previous_month(execution_date):
    lastmonth = execution_date.subtract(months=1)
    start_of_month = lastmonth.start_of('month')
    end_of_month = lastmonth.end_of('month')
    end_of_month = end_of_month.add(days=1)
    return start_of_month,end_of_month

def get_month_buffer_range(start_date,end_date,days_range_before_curr_date,days_range_after_curr_date,report_config):
    reporter_config_day_range = DATE_RANGE_CONFIG.get(report_config['name'], {})
    if reporter_config_day_range:
        start_day_range_month =reporter_config_day_range.get('MONTH_RANGE_QUERY_BEFORE',-1)
        end_day_range_month =reporter_config_day_range.get('DAY_MONTH_RANGE_QUERY_AFTER',1)

    start_date_using_range = start_date.add(days=days_range_before_curr_date).end_of('day')
    end_date_using_range = end_date.add(days=days_range_after_curr_date).start_of('day')
    new_start_date_with_month_buffer = start_date_using_range.add(days=start_day_range_month).end_of('day')
    new_end_date_with_month_buffer = end_date_using_range.add(days=end_day_range_month).start_of('day')

    return new_start_date_with_month_buffer,new_end_date_with_month_buffer


def get_date_range_for_mtd_schedule(execution_date):

    start_day = START_OF_FIRST_WEEK_DAY
    start_month = execution_date.month
    start_year = execution_date.year
    end_month = execution_date.month
    end_year = execution_date.year
    if execution_date.day in range(START_OF_SECOND_WEEK_DAY, START_OF_THIRD_WEEK_DAY):
        end_day = START_OF_SECOND_WEEK_DAY
    if execution_date.day in range(START_OF_THIRD_WEEK_DAY, START_OF_FOURTH_WEEK_DAY):
        end_day = START_OF_THIRD_WEEK_DAY
    if execution_date.day >= START_OF_FOURTH_WEEK_DAY:
        end_day = START_OF_FOURTH_WEEK_DAY
    if execution_date.day in range(START_OF_FIRST_WEEK_DAY, START_OF_SECOND_WEEK_DAY):
        end_day = START_OF_FIRST_WEEK_DAY
        if execution_date.month != START_MONTH_NUMBER:
            start_month = execution_date.month - 1
        else:
            start_month = LAST_MONTH_NUMBER
            start_year = execution_date.year - 1
    start_date = pendulum.datetime(start_year, start_month, start_day)
    end_date = pendulum.datetime(end_year, end_month, end_day)

    return start_date,end_date


def get_formatted_date(d):
    return d.format('YYYY-MM-DD')

def get_day_range(report_config):
    start_day = -3;
    end_day = 3;
    reporter_config_day_range = DATE_RANGE_CONFIG.get(report_config['name'], {})
    if reporter_config_day_range:
        start_day =reporter_config_day_range.get('DAY_RANGE_QUERY_BEFORE',-3)
        end_day =reporter_config_day_range.get('DAY_RANGE_QUERY_AFTER',3)
    return start_day,end_day


def get_hive_queue_name(report_config):
    reporter_config_queue = DATE_RANGE_CONFIG.get(report_config['name'], {})
    return reporter_config_queue.get('HIVE_QUEUE_NAME','torch')

def format_query(query, start_date, end_date, execution_date, merchant_id, start_day_range, end_day_range,report_config):
    start_date_buffer_month,end_date_buffer_month =get_month_buffer_range(start_date,end_date,start_day_range,end_day_range,report_config)

    period = pendulum.period(start_date_buffer_month, end_date_buffer_month)
    month_set = set([d.month for d in period.range('days')])
    year_set = set([d.year for d in period.range('days')])
    transaction_types = set(report_config.get('transactionType', [0]))

    context = {
        'YEARS': ",".join(str(year) for year in year_set),
        'MONTHS': ",".join(str(month) for month in month_set),
        'START_DATE': get_formatted_date(start_date),
        'START_TIME': start_date,
        'START_DAY': start_date.day,
        'START_MONTH': start_date.month,
        'START_YEAR': start_date.year,
        'END_DATE': get_formatted_date(end_date),
        'END_TIME': end_date,
        'END_DAY': end_date.day,
        'END_MONTH': end_date.month,
        'END_YEAR': end_date.year,
        'EXECUTION_DATE': get_formatted_date(execution_date),
        'EXECUTION_TIME': execution_date,
        'EXECUTION_DAY': execution_date.day,
        'EXECUTION_MONTH': execution_date.month,
        'EXECUTION_YEAR': execution_date.year,
        'MERCHANT_ID': merchant_id,
        'BEFORE_DAY_RANGE': start_day_range,
        'AFTER_DAY_RANGE': end_day_range,
        'TRANSACTION_TYPES': ",".join(str(year) for year in transaction_types),
    }

    return apply_template(query.encode(), context)

def is_query_modifyable(report_config):
    query =report_config['query']['test_query'];
    if ("insert " in query) or ("INSERT " in query) or ("Insert " in query):
        print("Insert statement found in query")
        return True
    if ("update " in query) or ("UPDATE " in query) or ("Update " in query):
        print("Update statement found in query")
        return True
    else :
        return False
def read_report_config(filename):
    config_filepath = os.path.join(filename)
    report_config = json.load(open(config_filepath))
    logging.info("Report config ({})".format(report_config))
    return report_config

def read_query_template(query_template_filename):
    query_template_filepath = os.path.join(QUERY_TEMPLATES_DIR, query_template_filename)
    return open(query_template_filepath).read()


def read_email_template(email_template_filename):
    email_template_filepath = os.path.join(EMAIL_TEMPLATES_DIR, email_template_filename)
    return open(email_template_filepath).read()


def create_dir_for_file(file_path):
    dir_path = os.path.dirname(file_path)
    logging.info("Creating directory for path {}".format(dir_path))
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)
        logging.info("Created directory for path {}".format(dir_path))


def run_cmd(cmd):
    logging.info("Running subprocess: cmd({})".format(cmd))
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True)
    stdout, _ = process.communicate()
    return_code = process.returncode
    if return_code == 0:
        logging.info("Running subprocess completed: stdout({})".format(stdout))
        return stdout
    else:
        raise Exception("Running subprocess failed: return_code({}), stdout({})".format(return_code, stdout))


def run_stream_cmd(cmd):
    logging.info("Running subprocess: cmd({})".format(cmd))
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True)
    stdout, _ = process.communicate()
    return_code = process.returncode
    if return_code == 0:
        return stdout
    else:
        raise Exception("Running subprocess failed: return_code({}), stdout({})".format(return_code, stdout))

def run_stream_as_list_cmd(cmd):
    logging.info("Running subprocess: cmd({})".format(cmd))
    out = subprocess.check_output(cmd).split("\n")
    return out


def run_cmd_with_exception(cmd):
    logging.info("Running subprocess with handling exception : cmd({})".format(cmd))
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True)
    stdout, _ = process.communicate()
    return_code = process.returncode
    if return_code == 0:
        logging.info("Running subprocess completed: stdout({})".format(stdout))
        return stdout
    else:
        logging.error("Running subprocess failed: return_code({}), stdout({})".format(return_code, stdout))


def write_column_headers_to_file(file_path, content):
    cmd = "echo '{content}' | cat - '{file_path}' > '{temp_path}' && mv '{temp_path}' '{file_path}'".format(
        content=content,
        file_path=file_path,
        temp_path="{}.swp".format(file_path)
    )
    run_cmd(cmd)


def remove_file_pattern(file_path_pattern):
    cmd = "rm -rf {file_path_pattern}".format(file_path_pattern=file_path_pattern)
    run_cmd_with_exception(cmd)

def remove_file_pattern_using_airflow_user(file_path_pattern):
    cmd = "sudo -u {airflow_user} rm -rf {file_path_pattern}".format(airflow_user=HDFS_USER_ID, file_path_pattern=file_path_pattern)
    run_cmd_with_exception(cmd)


def touch_file_name(file_name):
    cmd = "touch {file_path_pattern}".format(file_path_pattern=file_name)
    run_cmd_with_exception(cmd)

def create_dirs_for_user(dir_path):
    cmd = "sudo -u {airflow_user} mkdir -p {dir_path}".format(airflow_user=HDFS_USER_ID, dir_path=dir_path)
    run_cmd(cmd)

def chmod_for_user(dir_path):
    cmd = "sudo -u {airflow_user} chmod -R 777 {dir_path}".format(airflow_user=HDFS_USER_ID, dir_path=dir_path)
    run_cmd_with_exception(cmd)

def line_count(file_path):
    cmd = "wc -l {file_path}".format(file_path=file_path)
    return int(run_cmd(cmd).decode().strip().split(' ')[0].strip())

def write_units_to_file(tsv_file_path, csv_file_path, python_file_name, execution_date):
    logging.info("python file path is {}".format(PYTHON_FILES_DIR+"/"+python_file_name))
    processor = imp.load_source("", PYTHON_FILES_DIR+"/"+python_file_name)
    processor.process(tsv_file_path, csv_file_path, execution_date)


def convert_tsv_to_custom_csv(tsv_file_path, csv_file_path, python_file_name, execution_date):
    write_units_to_file(tsv_file_path, csv_file_path, python_file_name, execution_date)


def add_file_to_zip(input_file_path, zip_file_path):
    zip_file = ZipFile(zip_file_path, mode='w', compression=ZIP_DEFLATED, allowZip64=True)
    zip_file.write(input_file_path, os.path.basename(input_file_path))
    zip_file.close()


def determine_execution_date(**kwargs):
    if kwargs['execution_date'].second != 0:
        # Manual run
        execution_date = kwargs['prev_execution_date']
    else:
        # Scheduled or backfill run
        execution_date = kwargs['next_execution_date']

    return execution_date


def days_between(d1, d2):
    try:
        try:
            d1 = datetime.strptime(d1, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            d1 = datetime.strptime(d1, "%Y-%m-%d")
    except ValueError:
        return 0;
    return abs((d2 - d1).days)


def input_date_fall_in_between(input_date, start_date, end_date):
    if get_formatted_date(start_date)<= get_formatted_date(input_date) < get_formatted_date(end_date) :
        return True
    else :
        return False


def get_final_report_file_name(query_file_name, temp_hdfs_output_dir):
    return temp_hdfs_output_dir + "/final_" + query_file_name

def get_query_file_name(end_date, execution_date, report_config, start_date):
    return report_config['name'] + "_" + get_formatted_date(
        start_date) + "_" + get_formatted_date(end_date) + "__" + get_formatted_date(
        execution_date) + "_tenant_query_output.csv";

def get_header_file_name(end_date, execution_date, report_config, start_date):
    return report_config['name'] + "_" + get_formatted_date(
        start_date) + "_" + get_formatted_date(end_date) + "__" + get_formatted_date(
        execution_date) + "_tenant_report_headers.csv";

def isBlank (myString):
    return not (myString and myString.strip())

def get_report_category(report_config):
    if 'SUMMARY' in report_config['name']:
        return 'SUMMARY'
    elif 'MISMATCH' in report_config['name']:
        return 'MIS_MATCH'
    elif 'MISSING' in report_config['name']:
        return 'MISSING'
    else :
        return 'MATCH'


def get_report_type(report_config):
    if 'DAILY' in report_config['name']:
        return 'DAILY'
    else :
        return 'MONTHLY'
