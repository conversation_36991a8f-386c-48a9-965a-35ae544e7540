import os
from datetime import timed<PERSON><PERSON>

import pendulum
from airflow import DAG

from mcp_reports.torch.config.config import REPORT_CONFIG_DIR, SFTP_CONFIG_DIR, REPORT_TEST_QUERY_CONFIG_DIR, TEST_QUERY_CONFIG, REPORT_CLEANUP_CONFIG_DIR
from mcp_reports.torch.operators import execute_hive_query_operator, \
    generate_output_report_operator, email_generated_report_operator, upload_to_docstore_operator, \
    trigger_sftp_file_fetch_operator, email_sftp_file_fetch_ack_operator, delete_tenant_file_operator, \
    clean_up_all_tmp_files_operator, update_match_transactions_operator, test_execute_hive_query_operator, \
    update_miss_match_transactions_operator, update_missing_transactions_operator
from mcp_reports.torch.utils.common import read_report_config

AIRFLOW_DEFAULT_ARGS = {
    'owner': 'phonepe_mcp_airflow_user',
    'depends_on_past': False,
    'email': ['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': True,
    'retries': 2,
    'retry_delay': timedelta(minutes=10),
    'start_date': pendulum.datetime(2021, 9, 1)
}


def generate_dag(dag_id, config):
    return DAG(
        dag_id,
        default_args=AIRFLOW_DEFAULT_ARGS,
        description=config.get('description', config['name']),
        schedule_interval=config['schedule'],
        catchup=False,
    )


def configure_dag(dag, report_config):
    query_source = report_config['query']['source']
    if query_source == 'hive':
        execute_query_op = execute_hive_query_operator(dag, report_config)
    else:
        raise Exception('Invalid query source supplied')

    generate_report_op = generate_output_report_operator(dag, report_config)
    upload_docstore_op = upload_to_docstore_operator(dag, report_config)
    clean_up_all_tmp_files_op = clean_up_all_tmp_files_operator(dag, report_config)

    execute_query_op >> generate_report_op >> upload_docstore_op

    upstream_op = upload_docstore_op

    if 'email' in report_config:
        email_report_op = email_generated_report_operator(dag, report_config)
        upstream_op >> email_report_op
        upstream_op = email_report_op

    if 'UPDATE_MATCH_TRANSACTIONS' in report_config.get('name'):
        update_match_transactions_op = update_match_transactions_operator(dag, report_config)
        upstream_op >> update_match_transactions_op
        upstream_op = update_match_transactions_op

    if 'MISMATCH_TRANSACTIONS' in report_config.get('name'):
        update_missmatch_transactions_op = update_miss_match_transactions_operator(dag, report_config)
        upstream_op >> update_missmatch_transactions_op
        upstream_op = update_missmatch_transactions_op

    if 'MISSING_TRANSACTIONS' in report_config.get('name'):
        update_missing_transactions_op = update_missing_transactions_operator(dag, report_config)
        upstream_op >> update_missing_transactions_op
        upstream_op = update_missing_transactions_op


    upstream_op >> clean_up_all_tmp_files_op



def config_sftp_dag(dag, sftp_config):
    trigger_sftp_file_fetch_op = trigger_sftp_file_fetch_operator(dag, sftp_config)
    email_sftp_file_fetch_ack_op = email_sftp_file_fetch_ack_operator(dag, sftp_config)
    trigger_sftp_file_fetch_op >> email_sftp_file_fetch_ack_op


def configure_cleanup_dag(dag, report_cleanup_config):
    delete_tenant_file_op = delete_tenant_file_operator(dag, report_cleanup_config)
    delete_tenant_file_op


def configure_test_query_dag(dag, report_query_config):
    report_config = generate_report_config()
    test_execute_hive_query_op = test_execute_hive_query_operator(dag, report_config)
    generate_report_op = generate_output_report_operator(dag, report_config)
    upload_docstore_op = upload_to_docstore_operator(dag, report_config)
    clean_up_all_tmp_files_op = clean_up_all_tmp_files_operator(dag, report_config)

    REPORT_TO_BE_GENERATED = TEST_QUERY_CONFIG.get('REPORT_TO_BE_GENERATED', False)
    if REPORT_TO_BE_GENERATED:
        email_report_op = email_generated_report_operator(dag, report_config)
        test_execute_hive_query_op >> generate_report_op >> upload_docstore_op >> clean_up_all_tmp_files_op >> email_report_op
    else :
        test_execute_hive_query_op


def generate_report_config():
    report_config = {}
    report_config['name'] = TEST_QUERY_CONFIG.get('REPORT_CONFIG_NAME', 'TORCH_AIRTELDTHDIRECT_MISMATCH_TRANSACTIONS')
    report_config['query'] = {}
    query_config = {}
    query_config['source'] = 'hive'
    query_config['templatePath'] = [report_config['name']]
    query_config['test_query']=TEST_QUERY_CONFIG.get('QUERY', '')
    report_config['query'] = query_config
    report_config['merchantId'] = TEST_QUERY_CONFIG.get('REPORT_CONFIG_MERCHANT_ID', 'AIRTELDTHDIRECT')
    report_config['pythonFile'] = TEST_QUERY_CONFIG.get('REPORT_CONFIG_PYTHON_FILE', 'default.py')
    report_config['fileName'] = TEST_QUERY_CONFIG.get('REPORT_CONFIG_FILE_NAME',
                                                      'MISMATCH_TRANSACTIONS_FOR_{MERCHANT_ID}_{START_DATE}_{END_DATE}.csv')
    report_config['email'] = {}
    email_config = {}
    email_config['to'] = TEST_QUERY_CONFIG.get('REPORT_CONFIG_EMAIL_TO', '<EMAIL>').split(",")
    email_config['subject'] = TEST_QUERY_CONFIG.get('REPORT_CONFIG_EMAIL_SUBJECT','Bucketing of mismatch transactions for {MERCHANT_ID} from {START_DATE} to {END_DATE}')
    email_config['templatePath'] = TEST_QUERY_CONFIG.get('REPORT_CONFIG_EMAIL_TEMPLATE','torch_mismatch_transactions.html')
    report_config['email'] = email_config
    report_config['fileType'] = '.csv'
    columns = TEST_QUERY_CONFIG.get('REPORT_COLUMN', '');
    report_config['columns'] = columns.split(",")
    return report_config


def setup_cleanup_tenant(report_cleanup_configs):
    for report_cleanup_config in report_cleanup_configs:
        dag_id = '{}_v{}'.format(report_cleanup_config['name'], report_cleanup_config.get('version', 1))
        dag = generate_dag(dag_id, report_cleanup_config)
        configure_cleanup_dag(dag, report_cleanup_config)
        globals()[dag_id] = dag

def setup_test_query(report_test_query_configs):
    for report_test_query_config in report_test_query_configs:
        dag_id = '{}_v{}'.format(report_test_query_config['name'], report_test_query_config.get('version', 1))
        dag = generate_dag(dag_id, report_test_query_config)
        configure_test_query_dag(dag, report_test_query_config)
        globals()[dag_id] = dag


def main():
    report_config_files = [os.path.join(dp, f) for dp, dn, filenames in os.walk(REPORT_CONFIG_DIR) for f in filenames]
    report_configs = [read_report_config(rcf) for rcf in report_config_files]

    for report_config in report_configs:
        dag_id = '{}_v{}'.format(report_config['name'], report_config.get('version', 1))
        dag = generate_dag(dag_id, report_config)
        configure_dag(dag, report_config)
        globals()[dag_id] = dag

    sftp_config_files = [os.path.join(dp, f) for dp, dn, filenames in os.walk(SFTP_CONFIG_DIR) for f in filenames]
    sftp_configs = [read_report_config(scf) for scf in sftp_config_files]

    for sftp_config in sftp_configs:
        dag_id = '{}_v{}'.format(sftp_config['name'], report_config.get('version', 1))
        dag = generate_dag(dag_id, sftp_config)
        config_sftp_dag(dag, sftp_config)
        globals()[dag_id] = dag

    report_cleanup_config_files = [os.path.join(dp, f) for dp, dn, filenames in os.walk(REPORT_CLEANUP_CONFIG_DIR) for f in filenames]
    report_cleanup_configs = [read_report_config(scf) for scf in report_cleanup_config_files]

    setup_cleanup_tenant(report_cleanup_configs)

    report_test_query_files = [os.path.join(dp, f) for dp, dn, filenames in os.walk(REPORT_TEST_QUERY_CONFIG_DIR) for f in filenames]
    report_test_query_configs = [read_report_config(scf) for scf in report_test_query_files]
    setup_test_query(report_test_query_configs)


main()
