import logging
import uuid

from hive.hook.secured_hive_cli_hook import Secure<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ook
import traceback

from mcp_reports.merchantmandates.config.config import HDFS_USER_ID, TEMP_TSV_HDFS_PATH, TEMP_TSV_LOCAL_PATH, \
    TEMP_CSV_LOCAL_PATH, REALM, TEMP_CSV_HDFS_PATH, HDFS_CLEANUP_PATH
from mcp_reports.merchantmandates.utils.common import read_query_template, format_query, \
    get_formatted_date, \
    append_to_file_top, apply_template, read_email_template, \
    convert_tsv_to_custom_csv, remove_file_pattern, remove_file_pattern_using_airflow_user, line_count, \
    add_file_to_zip, determine_execution_date
from mcp_reports.merchantmandates.utils.hdfs import delete_from_hdfs, create_hdfs_dir, concat_hdfs_dir_to_local, \
    concat_local_to_hdfs_dir
from mcp_reports.merchantmandates.zencast_client import Zencast<PERSON><PERSON>


def get_file_paths(report_name, file_name, execution_date, query):
    query = query.replace("/", "_") if query is not None else query
    temp_tsv_local_path = TEMP_TSV_LOCAL_PATH.format(REPORT_NAME=report_name,
                                                     EXECUTION_DATE=get_formatted_date(execution_date),
                                                     QUERY=query)
    temp_tsv_hdfs_path = TEMP_TSV_HDFS_PATH.format(REPORT_NAME=report_name,
                                                   EXECUTION_DATE=get_formatted_date(execution_date),
                                                   QUERY=query)

    temp_csv_hdfs_path = TEMP_CSV_HDFS_PATH.format(REPORT_NAME=report_name,
                                                   EXECUTION_DATE=get_formatted_date(execution_date))
    temp_csv_local_path = TEMP_CSV_LOCAL_PATH + file_name.format(EXECUTION_DATE=get_formatted_date(execution_date))
    temp_csv_local_path_email = TEMP_CSV_LOCAL_PATH + "MMS_" + file_name.format(
        EXECUTION_DATE=get_formatted_date(execution_date))
    temp_hdfs_output_dir = temp_tsv_hdfs_path[:-4]
    temp_csv_hdfs_dir = temp_csv_hdfs_path[:-4]

    return temp_tsv_local_path, temp_tsv_hdfs_path, temp_hdfs_output_dir, temp_csv_local_path, temp_csv_hdfs_dir, temp_csv_local_path_email


def execute_hive_query(report_config, **kwargs):
    execution_date = determine_execution_date(**kwargs)
    for template_path in report_config['query']['templatePath']:
        query_template = read_query_template(template_path)
        _, _, temp_hdfs_output_dir, _, _, _ = get_file_paths(report_config['name'], report_config['fileName'],
                                                             execution_date,
                                                             template_path)

        hql = format_query(query_template, execution_date)
        hql = """
        set tez.queue.name = default; 
        set hive.exec.compress.output=false; 
        set hive.merge.tezfiles=true;
        INSERT OVERWRITE DIRECTORY '{report_directory}' ROW FORMAT DELIMITED FIELDS TERMINATED BY '\t' STORED AS TEXTFILE
        """.format(report_directory=temp_hdfs_output_dir) + hql
        hive_hook = SecuredHiveCliHook(user=HDFS_USER_ID, realm=REALM)

        logging.info("Hql query ({})".format(hql))

        create_hdfs_dir(temp_hdfs_output_dir)
        kwargs['ti'].xcom_push("temp_csv_hdfs_dir", temp_hdfs_output_dir)
        hive_hook.run_cli(hql)


def generate_output_report(report_config, **kwargs):
    temp_csv_local_path = ""
    execution_date = determine_execution_date(**kwargs)
    columns = ",".join(report_config['columns'])
    if report_config['fileType'] == '.psv':
        columns = "|".join(report_config['columns'])
    clean_up_csv_if_present = True
    temp_csv_hdfs_dir = ""
    for template_path in report_config['query']['templatePath']:
        temp_tsv_local_path, _, temp_hdfs_output_dir, temp_csv_local_path, temp_csv_hdfs_dir, _ = get_file_paths(
            report_config['name'], report_config['fileName'], execution_date, template_path)
        if clean_up_csv_if_present:
            remove_file_pattern(temp_csv_local_path)
            remove_file_pattern_using_airflow_user(temp_tsv_local_path)
            clean_up_csv_if_present = False
        concat_hdfs_dir_to_local(temp_hdfs_output_dir, temp_tsv_local_path)
        convert_tsv_to_custom_csv(temp_tsv_local_path, temp_csv_local_path, report_config['pythonFile'], execution_date)
        delete_from_hdfs(temp_hdfs_output_dir)
        remove_file_pattern_using_airflow_user(temp_tsv_local_path)
    append_to_file_top(temp_csv_local_path, columns)
    concat_local_to_hdfs_dir(temp_csv_hdfs_dir, temp_csv_local_path)
    remove_file_pattern(temp_csv_local_path)



def clean_up_all_tmp_files(report_config, **kwargs):
    execution_date = determine_execution_date(**kwargs)
    template_path = report_config['query']['templatePath'][0]
    temp_tsv_local_path, temp_tsv_hdfs_path, temp_hdfs_output_dir, temp_csv_local_path, temp_csv_hdfs_dir, temp_csv_local_path_email = \
        get_file_paths(
        report_config['name'], report_config['fileName'], execution_date, template_path)

    if temp_csv_local_path_email:
        logging.info("Cleaning csv directory path :({})".format(temp_csv_local_path_email))
        temp_zip_local_path_email = temp_csv_local_path_email[:-3] + "zip"
        remove_file_pattern_using_airflow_user(temp_csv_local_path_email)
        remove_file_pattern(temp_zip_local_path_email)
    if temp_csv_local_path:
        remove_file_pattern(temp_csv_local_path)
    if temp_tsv_local_path:
        remove_file_pattern_using_airflow_user(temp_tsv_local_path)
    if temp_hdfs_output_dir:
        delete_from_hdfs(temp_hdfs_output_dir)
    if temp_csv_hdfs_dir:
        logging.info("Cleaning csv hdfs path :({})".format(temp_csv_hdfs_dir))
        delete_from_hdfs(temp_csv_hdfs_dir)

    logging.info("Cleaning from all the tmp location is done for config {}".format(report_config))

def email_generated_report(report_config, **kwargs):
    if not report_config.get('email'):
        logging.info("Skipping email. No email config found.")
        return

    email_config = report_config['email']
    execution_date = determine_execution_date(**kwargs)
    _, _, _, _, temp_csv_hdfs_dir, temp_csv_local_path_email = get_file_paths(
        report_config['name'], report_config['fileName'], execution_date, None)

    concat_hdfs_dir_to_local(temp_csv_hdfs_dir, temp_csv_local_path_email)
    temp_zip_local_path_email = temp_csv_local_path_email[:-3] + "zip"
    add_file_to_zip(temp_csv_local_path_email, temp_zip_local_path_email)

    if line_count(temp_csv_local_path_email) == 1:
        logging.info("Skipping email. No records present.")
        remove_file_pattern_using_airflow_user(temp_csv_local_path_email)
        remove_file_pattern(temp_zip_local_path_email)
        delete_from_hdfs(temp_csv_hdfs_dir)
        return

    logging.info("Generating requestId for sending email via Zencast")
    request_id = str(uuid.uuid1())

    logging.info("Pulling fileId and fileName from XCom")

    context = {
        'EXECUTION_DATE': get_formatted_date(execution_date)
    }
    recipients = email_config['to']
    subject = email_config['subject'].format(**context)
    content = apply_template(read_email_template(
        email_config['templatePath']
    ).encode(), context)

    file_name = report_config['fileName'].format(**context)

    logging.info("Initializing Zencast client")
    zencast_client = ZencastClient()

    logging.info(
        "Sending email to({}), subject({}) and requestId({}))".format(
            recipients[0], subject, request_id))
    try:
        zencast_client.send_email(request_id, str(recipients[0]), str(content), subject, file_name,
                                  read_local_file(temp_csv_local_path_email))
    except Exception:
        logging.error("Failed to send E-Mail. Exception: %s" % traceback.format_exc())

    remove_file_pattern_using_airflow_user(temp_csv_local_path_email)
    remove_file_pattern(temp_zip_local_path_email)
    delete_from_hdfs(temp_csv_hdfs_dir)

def remove_hdfs_files(report_file_path):
    logging.info("Removing file from path of report {} ".format(report_file_path))
    delete_from_hdfs(report_file_path)

def remove_files(**kwargs):
    remove_hdfs_files(HDFS_CLEANUP_PATH)

def read_local_file(file_path):
    with open(file_path, "rb") as fil:
        return fil.read()
