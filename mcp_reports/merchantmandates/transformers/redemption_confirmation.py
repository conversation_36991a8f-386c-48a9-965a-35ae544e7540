import csv
import logging
import requests
from mcp_reports.merchantmandates.config.config import MMS_SERVICE_URL
from mcp_reports.merchantmandates.olympus_im_client import OlympusIMClient
from mcp_reports.merchantmandates.config.config import OLYMPUS_PROTOCOL, OLYMPUS_HOST, \
     OLYMPUS_PORT, OLYMPUS_CLIENT_ID, OLYMPUS_CLIENT_KEY
from requests.adapters import HTTPAdapter, <PERSON>try


def process(tsv_file_path, csv_file_path, execution_date):
    with open(csv_file_path, 'w') as csvfile:
        writer = csv.writer(csvfile, delimiter=',', quotechar='|', quoting=csv.QUOTE_MINIMAL)
        mms_endpoint = MMS_SERVICE_URL + "/v1/redemption/recon/"
        olympusClient = OlympusIMClient(OLYMPUS_PROTOCOL, OLYMPUS_HOST, OLYMPUS_PORT,
                                        OLY<PERSON><PERSON>_CLIENT_ID, OLYMPUS_CLIENT_KEY)
        tsv_file = open(tsv_file_path)
        reader = csv.reader(tsv_file, delimiter='\t')
        next(reader, None)
        lines = list(reader)
        logging.info("Number of rows found are: {}".format(len(lines)))
        s = requests.Session()
        retries = Retry(total=1,
                        backoff_factor=0.5,
                        status_forcelist=[400, 401, 402, 404, 500, 502, 503, 504],
                        method_whitelist=frozenset(['POST']))

        s.mount('https://', HTTPAdapter(max_retries=retries))
        for item in lines:
            item[0] = item[0] if item[0] != 'null' and item[0] != '\\N' else ""
            item[1] = item[1] if item[1] != 'null' and item[1] != '\\N' else ""
            merchant_id = item[0]
            merchant_transaction_id = item[1]
            try:
                logging.info("Calling mms for Redemption confirmation recon")
                headers = {'content-type': 'application/json', 'accept': 'application/json',
                           'Authorization': olympusClient.get_bearer_auth_token()}
                url = mms_endpoint + merchant_id + "/" + merchant_transaction_id + "?retriesLeft=1&backoffFactor=0"
                res = s.post(url, headers=headers, verify=False)
                if res.status_code == 200:
                    writer.writerow(item)
                else:
                    logging.error("Redemption confirmation recon failed with error code ({})".format(res.status_code))
                    raise Exception()
            except Exception as e:
                logging.error("Redemption confirmation recon failed with exception {}".format(e))
                raise e
