import csv
import logging

def process(tsv_file_path, csv_file_path, execution_date):
    with open(csv_file_path, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile, delimiter=',', quotechar='|', quoting=csv.QUOTE_MINIMAL)
        with open(tsv_file_path) as tsv_file:
            reader = csv.reader(tsv_file, delimiter='\t')

            # Read and write the header
            header = next(reader, None)
            if header:
                writer.writerow(header)

            lines = list(reader)
            logging.info("Number of rows found are: {}".format(len(lines)))

            # Sort the lines by the first column (date)
            lines.sort(key=lambda x: x[0])

            # Write the sorted lines to the CSV file
            for item in lines:
                writer.writerow(item)