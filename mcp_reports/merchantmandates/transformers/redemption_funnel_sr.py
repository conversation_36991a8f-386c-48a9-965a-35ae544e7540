import csv
import logging

def process(tsv_file_path, csv_file_path, execution_date):
    with open(csv_file_path, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile, delimiter=',', quotechar='|', quoting=csv.QUOTE_MINIMAL)

        # Read the TSV file
        with open(tsv_file_path, 'r') as tsv_file:
            reader = csv.reader(tsv_file, delimiter='\t')

            # Extract the header and write it with new columns
            header = next(reader, None)
            if header:
                header.extend([
                    'notification_callback_received_percentage',
                    'notification_callback_sent_to_category_percentage',
                    'category_confirmation_done_percentage',
                    'debit_triggered_percentage',
                    'debit_processed_callback_percentage',
                    'debit_callback_sent_to_category_percentage'
                ])
                writer.writerow(header)

            # Process each row, calculate percentages, and write to the CSV
            lines = list(reader)
            logging.info("Number of rows found are: {}".format(len(lines)))

            processed_lines = []
            for item in lines:
                # Calculate percentages
                notification_executed = int(item[2])
                notification_callback_received = int(item[3])
                notification_callback_sent_to_category = int(item[4])
                category_confirmation_done = int(item[5])
                debit_triggered = int(item[6])
                debit_processed_callback = int(item[7])
                debit_callback_sent_to_category = int(item[8])

                # Assuming notification_executed is the base for percentage calculations
                if notification_executed > 0:
                    percentages = [
                        (notification_callback_received / notification_executed) * 100,
                        (notification_callback_sent_to_category / notification_executed) * 100,
                        (category_confirmation_done / notification_executed) * 100,
                        (debit_triggered / notification_executed) * 100,
                        (debit_processed_callback / notification_executed) * 100,
                        (debit_callback_sent_to_category / notification_executed) * 100,
                    ]
                else:
                    percentages = [0] * 6

                # Append percentages to the row
                item.extend(percentages)
                processed_lines.append(item)

            # Sort lines by redemption_date and eventdata_categoryidentifier
            processed_lines.sort(key=lambda x: (x[0], x[1]))

            # Write sorted and updated data to CSV
            for item in processed_lines:
                writer.writerow(item)