#!/usr/bin python

import logging
import requests
from retrying import retry
import jwt
import time

HEADERS = {
    "Content-Type": "application/x-www-form-urlencoded",
    "Accept": "application/json"
}

class OlympusIMClient(object):

    def __init__(self, scheme, host, port, clientId, clientSecret):
        self.authToken = None
        self.scheme = scheme
        self.host = host
        self.port = port
        self.clientId = clientId
        self.clientSecret = clientSecret

    @retry(stop_max_attempt_number=3, wait_exponential_multiplier=10, wait_exponential_max=1000)
    def __login(self):
        url = "{}://{}:{}/olympus/im/v1/oauth/token".format(self.scheme, self.host, self.port)
        request = {
            "grant_type": "client_credentials",
            "client_id": self.clientId,
            "client_secret": self.clientSecret
        }

        try:
            response = requests.post(url=url, data=request, headers=HEADERS).json()
            self.authToken = response['access_token']
        except Exception as e:
            logging.error("Unable to login to OlympusIM (will attempt login 3 times): {}".format(e.message))
            raise e

    def get_auth_token(self):
        if self.authToken:
            decodedJwt = jwt.decode(self.authToken, options={"verify_signature": False, "verify_aud": False})
            issuedAt = decodedJwt['iat']
            expiresAt = decodedJwt['exp']
            currentTime = int(time.time())
            reloadAt = issuedAt + (expiresAt - issuedAt)/2
            if currentTime > reloadAt:
                try:
                    self.__login()
                except Exception:
                    logging.error("Failed to refresh token, returning older token")
            return self.authToken
        else:
            self.__login()
            return self.authToken

    def get_bearer_auth_token(self):
        return "O-Bearer " + str(self.get_auth_token())

