import yaml
import logging

CACHE = {}


def get_rosey_config(env, rosey_config_path, team_id, project_id):
  if is_stage_env(env) or is_prod_env(env):
    from configprovider import RoseyService
    response = RoseyService(rosey_config_path).get_project_config(team_id, project_id)
    if response.status_code != 200:
     raise Exception('Failed to fetch rosey config')
    else:
     return yaml.safe_load(response.content)

  else:
    return yaml.safe_load(open(rosey_config_path, 'r'))


def get_rosey_config_cached(env, rosey_config_path, team_id, project_id):

  logging.info("fetching rosey config for team_id ({}) and project_id ({})".format(team_id, project_id))
  cache_key = "{}__{}".format(team_id, project_id)

  if CACHE.get(cache_key):
    return CACHE.get(cache_key)

  else:
    CACHE[cache_key] = get_rosey_config(env, rosey_config_path, team_id, project_id)
    return CACHE[cache_key]


def is_stage_env(env):
    return env.lower() == 'stage'


def is_prod_env(env):
    return env.lower() == 'prod'
