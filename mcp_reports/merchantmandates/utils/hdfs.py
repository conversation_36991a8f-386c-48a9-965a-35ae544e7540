import logging
import os

from mcp_reports.merchantmandates.config.config import HDFS_USER_ID, HDFS_CMD
from mcp_reports.merchantmandates.utils.common import run_cmd, remove_file_pattern_using_airflow_user, \
    chmod_for_user, create_dirs_for_user

MKDIR_CMD = """-mkdir -p "{HDFS_DIR_PATH}" """
PUT_CMD = """-put -f "{LOCAL_PATH}" "{HDFS_PATH}" """
GET_CMD = """-get -f "{HDFS_PATH}" "{LOCAL_PATH}" """
RM_CMD = """-rm -r -f "{HDFS_PATH}" """
GETMERGE_CMD = """-getmerge "{HDFS_DIR}" "{LOCAL_PATH}" """


def run_hdfs_cmd(cmd, user_id=HDFS_USER_ID):
    logging.info("Running hdfs command: cmd({}), user_id({})".format(cmd, user_id))
    cmd = HDFS_CMD.format(USER_ID=user_id, CMD=cmd)
    logging.info("command is : ({})".format(cmd))
    run_cmd(cmd)


def create_hdfs_dir(dir_path):
    logging.info("Creating hdfs directory: dir_path({})".format(dir_path))
    create_dir_cmd = MKDIR_CMD.format(HDFS_DIR_PATH=dir_path)
    run_hdfs_cmd(create_dir_cmd)


def delete_from_hdfs(hdfs_path):
    logging.info("Deleting from hdfs: hdfs_path({})".format(hdfs_path))
    delete_cmd = RM_CMD.format(HDFS_PATH=hdfs_path)
    run_hdfs_cmd(delete_cmd)


def concat_hdfs_dir_to_local(hdfs_dir, local_path):
    logging.info("Writing content of hdfs_dir({}) to local_path({})".format(hdfs_dir, local_path))
    dir_path = os.path.dirname(local_path)
    create_dirs_for_user(dir_path)
    getmerge_cmd = GETMERGE_CMD.format(HDFS_DIR=hdfs_dir, LOCAL_PATH=local_path)
    run_hdfs_cmd(getmerge_cmd)
    chmod_for_user(dir_path)

def remove_crc_for_file_path(file_path):
    logging.info("Removing crc file for file_path({})".format(file_path))
    dir_path = os.path.dirname(file_path)
    file_name = os.path.basename(file_path)
    remove_file_pattern_using_airflow_user("{}/.{}.crc".format(dir_path, file_name))


def concat_local_to_hdfs_dir(hdfs_dir, local_path):
    remove_crc_for_file_path(local_path)
    logging.info("Writing content of local_path({}) to hdfs_dir({})".format(local_path, hdfs_dir))
    create_hdfs_dir(hdfs_dir)
    put_cmd = PUT_CMD.format(LOCAL_PATH=local_path, HDFS_PATH=hdfs_dir)
    run_hdfs_cmd(put_cmd)
