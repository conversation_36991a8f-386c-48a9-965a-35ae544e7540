{"name": "AO_REDEMPTION_FAILURE_REASON_DAG", "fileName": "AO_REDEMPTION_FAILURE_REASON_{EXECUTION_DATE}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["redemption/redemption_failure_report.hql"]}, "schedule": "0 23 * * *", "pythonFile": "redemption_sr.py", "columns": ["initiated_date", "failure_reason", "error_code", "mandate_redemptions_failed", "total_initiated", "failure_percentage"], "email": {"condition": {">": [{"var": "LINE_COUNT"}, 1]}, "to": ["<EMAIL>"], "subject": "AO_REDEMPTION_FAILURE_REASON_{EXECUTION_DATE}", "templatePath": "ao_redemption_failure_reason_report.html"}}