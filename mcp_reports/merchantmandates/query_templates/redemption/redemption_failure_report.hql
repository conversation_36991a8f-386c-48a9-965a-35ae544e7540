SELECT
    i.redemption_init_date AS initiated_date,
    f.failure_reason AS failure_reason,
    f.error_code AS error_code,
    COUNT(DISTINCT f.eventdata_merchanttransactionid) AS mandate_redemptions_failed,
    ti.total_initiated AS total_initiated,
    (COUNT(DISTINCT f.eventdata_merchanttransactionid) * 100.0 / ti.total_initiated) AS failure_percentage
FROM
    (
        SELECT
            eventdata_merchanttransactionid,
            eventdata_mandateid,
            DATE(concat(year,'-',SUBSTR(CONCAT(0,month),-2),'-',SUBSTR(CONCAT(0,day),-2))) AS redemption_init_date
        FROM foxtrot_stream.merchant_mandates_default
        WHERE
            CONCAT(year,SUBSTR(CONCAT(0,month),-2),SUBSTR(CONCAT(0,day),-2)) >= DATE_FORMAT(DATE_SUB(CURRENT_DATE, 3),'yyyyMMdd')
            AND eventtype IN ('REDEMPTION_EXECUTE_REQUEST_INIT')
    ) i
JOIN
    (
        SELECT
            eventdata_merchanttransactionid,
            eventdata_failurereason AS failure_reason,
            DATE(concat(year,'-',SUBSTR(CONCAT(0,month),-2),'-',SUBSTR(CONCAT(0,day),-2))) AS redemption_complete_date_2,
            CASE
                WHEN eventdata_failurereason IN ('REDEMPTION_FAILED', 'REDEMPTION_SKIPPED', 'NOTIFICATION_FAILED') THEN eventdata_cmsredemptionresponsedetails_errorcode
                WHEN eventdata_failurereason = 'NOTIFICATION_EXECUTION_FAILED' THEN eventData_cmsRedemptionResponseDetails_responseCode
                ELSE eventdata_errorcode
            END AS error_code
        FROM foxtrot_stream.merchant_mandates_default
        WHERE
            CONCAT(year,SUBSTR(CONCAT(0,month),-2),SUBSTR(CONCAT(0,day),-2)) >= DATE_FORMAT(DATE_SUB(CURRENT_DATE, 3),'yyyyMMdd')
            AND eventtype IN ('REDEMPTION_FAILED')
    ) f
ON UPPER(TRIM(i.eventdata_merchanttransactionid)) = UPPER(TRIM(f.eventdata_merchanttransactionid))
    AND f.redemption_complete_date_2 >= i.redemption_init_date
JOIN
    (
        SELECT
            redemption_init_date,
            COUNT(DISTINCT eventdata_merchanttransactionid) AS total_initiated
        FROM (
            SELECT
                eventdata_merchanttransactionid,
                DATE(concat(year,'-',SUBSTR(CONCAT(0,month),-2),'-',SUBSTR(CONCAT(0,day),-2))) AS redemption_init_date
            FROM foxtrot_stream.merchant_mandates_default
            WHERE
                CONCAT(year,SUBSTR(CONCAT(0,month),-2),SUBSTR(CONCAT(0,day),-2)) >= DATE_FORMAT(DATE_SUB(CURRENT_DATE, 3),'yyyyMMdd')
                AND eventtype IN ('REDEMPTION_EXECUTE_REQUEST_INIT')
        ) sub_initiated
        GROUP BY redemption_init_date
    ) ti
ON i.redemption_init_date = ti.redemption_init_date
GROUP BY i.redemption_init_date, f.failure_reason, f.error_code, ti.total_initiated