SELECT
    DATE(concat(t1.year,'-',<PERSON><PERSON><PERSON><PERSON>(CONCAT(0,t1.month),-2),'-',<PERSON><PERSON><PERSON><PERSON>(CONCAT(0,t1.day),-2))) AS redemption_date,
    t1.eventdata_categoryidentifier AS category,
    COUNT(DISTINCT t1.eventdata_merchanttransactionid) AS notification_executed,
    COUNT(DISTINCT t2.eventdata_merchanttransactionid) AS notification_callback_received,
    COUNT(DISTINCT t3.eventdata_merchanttransactionid) AS notification_callback_sent_to_category,
    COUNT(DISTINCT t4.eventdata_merchanttransactionid) AS category_confirmation_done,
    COUNT(DISTINCT t5.eventdata_merchanttransactionid) AS debit_triggered,
    COUNT(DISTINCT t6.eventdata_merchanttransactionid) AS debit_processed_callback,
    COUNT(DISTINCT t7.eventdata_merchanttransactionid) AS debit_callback_sent_to_category
FROM
    (SELECT eventdata_merchanttransactionid, eventdata_categoryidentifier, year, month, day
     FROM foxtrot_stream.merchant_mandates_default
     WHERE eventtype = 'REDEMPTION_NOTIFICATION_EXECUTION'
       AND CONCAT(year,SUBSTR(CONCAT(0,month),-2),SUBSTR(CONCAT(0,day),-2)) >= DATE_FORMAT(DATE_SUB(CURRENT_DATE, 3),'yyyyMMdd')
    ) t1
LEFT JOIN
    (SELECT eventdata_merchanttransactionid
     FROM foxtrot_stream.merchant_mandates_default
     WHERE eventtype = 'REDEMPTION_NOTIFICATION'
    ) t2
ON t1.eventdata_merchanttransactionid = t2.eventdata_merchanttransactionid
LEFT JOIN
    (SELECT eventdata_merchanttransactionid
     FROM foxtrot_stream.merchant_mandates_default
     WHERE eventtype = 'CATEGORY_NOTIFICATION_CALLBACK'
    ) t3
ON t1.eventdata_merchanttransactionid = t3.eventdata_merchanttransactionid
LEFT JOIN
    (SELECT eventdata_merchanttransactionid
     FROM foxtrot_stream.merchant_mandates_default
     WHERE eventtype = 'REDEMPTION_CATEGORY_CONFIRMATION'
    ) t4
ON t1.eventdata_merchanttransactionid = t4.eventdata_merchanttransactionid
LEFT JOIN
    (SELECT eventdata_merchanttransactionid
     FROM foxtrot_stream.merchant_mandates_default
     WHERE eventtype = 'REDEMPTION_CONFIRMATION_EXECUTION'
    ) t5
ON t1.eventdata_merchanttransactionid = t5.eventdata_merchanttransactionid
LEFT JOIN
    (SELECT eventdata_merchanttransactionid
     FROM foxtrot_stream.merchant_mandates_default
     WHERE eventtype = 'REDEMPTION_CONFIRMATION'
    ) t6
ON t1.eventdata_merchanttransactionid = t6.eventdata_merchanttransactionid
LEFT JOIN
    (SELECT eventdata_merchanttransactionid
     FROM foxtrot_stream.merchant_mandates_default
     WHERE eventtype = 'CATEGORY_REDEMPTION_CALLBACK'
       AND eventdata_mandateredemptionstate = 'SUCCESS'
    ) t7
ON t1.eventdata_merchanttransactionid = t7.eventdata_merchanttransactionid
GROUP BY
    DATE(concat(t1.year,'-',SUBSTR(CONCAT(0,t1.month),-2),'-',SUBSTR(CONCAT(0,t1.day),-2))),
    t1.eventdata_categoryidentifier