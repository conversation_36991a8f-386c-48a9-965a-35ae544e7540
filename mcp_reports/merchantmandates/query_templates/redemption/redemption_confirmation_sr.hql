SELECT
    redemption_init_date,
    COUNT(DISTINCT t1.eventdata_merchanttransactionid) AS mandate_redemptions_confirmed,
    COUNT(DISTINCT CASE WHEN t2.eventdata_merchanttransactionid IS NOT NULL THEN t2.eventdata_merchanttransactionid END) / COUNT(DISTINCT t1.eventdata_merchanttransactionid) AS mandate_redemptions_SR
FROM
    (SELECT
        eventdata_mandateid,
        eventdata_merchanttransactionid,
        eventdata_merchantmandatetype,
        DATE(concat(year,'-',SUBSTR(CONCAT(0,month),-2),'-',SUBSTR(CONCAT(0,day),-2))) AS redemption_init_date
    FROM foxtrot_stream.merchant_mandates_default
    WHERE
        CONCAT(year,SUBSTR(CONCAT(0,month),-2),SUBSTR(CONCAT(0,day),-2)) >= DATE_FORMAT(DATE_SUB(CURRENT_DATE, 5),'yyyyMMdd')
        AND eventtype IN ('REDEMPTION_CATEGORY_CONFIRMATION')
    ) t1
    LEFT JOIN
    (
    SELECT
        eventdata_mandateid,
        eventdata_merchanttransactionid,
        DATE(concat(year,'-',SUBSTR(CONCAT(0,month),-2),'-',SUBSTR(CONCAT(0,day),-2))) AS redemption_complete_date_2
    FROM foxtrot_stream.merchant_mandates_default
    WHERE
        CONCAT(year,SUBSTR(CONCAT(0,month),-2),SUBSTR(CONCAT(0,day),-2)) >= DATE_FORMAT(DATE_SUB(CURRENT_DATE, 5),'yyyyMMdd')
        AND eventtype IN ('CATEGORY_REDEMPTION_CALLBACK')
        AND eventdata_mandateredemptionstate = 'SUCCESS'
    ) t2
    ON UPPER(TRIM(t1.eventdata_merchanttransactionid)) = UPPER(TRIM(t2.eventdata_merchanttransactionid))
        AND t2.redemption_complete_date_2 >= redemption_init_date

GROUP BY redemption_init_date