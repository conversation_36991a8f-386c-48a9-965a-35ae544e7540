{"name": "RO_MONTHLY_PRE_MEC_INSTRUMENT_SPLIT_REPORT", "fileName": "RO_{QUERY}_REPORT_{EXECUTION_DATE}", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["mec/monthly_pre_mec_instrument_split.hql"]}, "schedule": "0 4 * * *", "columns": ["payer_id", "reference_id", "merchant_id", "merchant_txn_id", "merchant_forward_txn_id", "payment_txn_id", "payment_forward_txn_id", "total_base_amount", "total_amount", "instrument_split_amount", "instrument_type", "refund_state", "workflow_state", "tenant", "created_at", "completed_date"], "email": {"condition": {">": [{"var": "LINE_COUNT"}, 1]}, "to": ["<EMAIL>"], "subject": "RO_MONTHLY_PRE_MEC_INSTRUMENT_SPLIT_REPORT_{EXECUTION_DATE}", "templatePath": "monthly_mec_instrument_split.html"}}