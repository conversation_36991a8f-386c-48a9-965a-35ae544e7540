{"name": "RO_DAILY_TENANT_ANOMALY_FAILED", "fileName": "RO_FAILED_ANOMALY_REPORT_{EXECUTION_DATE}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["ad/failed_refunds.hql"]}, "schedule": "0 2 * * *", "pythonFile": "tenant_anomaly.py", "columns": ["workflow_id", "refund_status", "type", "workflow_state", "workflow_created_at", "workflow_updated_at", "tenant", "merchant_id", "merchant_txn_id", "merchant_fwd_txn_id", "payment_txn_id", "payment_fwd_txn_id", "amount", "total_amount", "response_code", "payment_retry_count"], "email": {"condition": {">": [{"var": "LINE_COUNT"}, 1]}, "to": ["<EMAIL>"], "subject": "RO Daily Anomaly Report (failures) - {EXECUTION_DATE}", "templatePath": "default.html", "disabled": true}}