{"name": "RO_WEEKLY_REFUNDED", "fileName": "RO_{QUERY}_REPORT_{EXECUTION_DATE}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["recon/weekly_refunded.hql"]}, "schedule": "0 0 * * 1", "columns": ["created_at", "merchant_id", "merchant_txn_id", "reference_id", "payment_txn_id", "payment_forward_txn_id", "amount", "status", "workflow_status", "response_code"], "email": {"condition": {">": [{"var": "LINE_COUNT"}, 1]}, "to": ["<EMAIL>", "<EMAIL>", "<EMAIL>"], "subject": "RO Daily Report - Refunds stuck in REFUNDED - {EXECUTION_DATE}", "templatePath": "default.html"}}