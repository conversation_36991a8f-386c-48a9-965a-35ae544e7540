{"name": "RO_AGEING_VIEW_DAILY", "fileName": "RO_{QUERY}_REPORT_{EXECUTION_DATE}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["recon/ageing_daily.hql"]}, "schedule": "0 2 * * *", "pythonFile": "aging_ro_daily.py", "columns": ["created_at", "tenant", "merchant_id", "merchant_txn_id", "reference_id", "payment_txn_id", "payment_forward_txn_id", "amount", "status", "workflow_status", "response_code"]}