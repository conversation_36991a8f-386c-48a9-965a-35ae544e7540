{"name": "RO_DAILY_INIT", "fileName": "RO_{QUERY}_REPORT_{EXECUTION_DATE}.csv", "fileType": ".csv", "version": 1, "query": {"source": "hive", "templatePath": ["recon/daily_init.hql"]}, "schedule": "0 2 * * *", "columns": ["created_at", "merchant_id", "merchant_txn_id", "reference_id", "payment_txn_id", "payment_forward_txn_id", "amount", "status", "workflow_status", "response_code"], "email": {"condition": {">": [{"var": "LINE_COUNT"}, 1]}, "to": ["<EMAIL>", "<EMAIL>", "<EMAIL>"], "subject": "RO Daily Report - Refunds stuck before payment init - {EXECUTION_DATE}", "templatePath": "default.html"}}