from airflow.operators.python_operator import PythonOperator

from mcp_reports.ro.tasks import execute_hive_query, generate_output_report, \
    email_generated_report, remove_files, clean_up_all_tmp_files, upload_to_docstore, process_report_file


def execute_hive_query_operator(dag, report_config):
    return PythonOperator(
        task_id='execute_hive_query',
        provide_context=True,
        python_callable=execute_hive_query,
        op_kwargs={
            'report_config': report_config,
        },
        dag=dag,
    )


def generate_output_report_operator(dag, report_config):
    return PythonOperator(
        task_id='generate_output_report',
        provide_context=True,
        python_callable=generate_output_report,
        op_kwargs={
            'report_config': report_config,
        },
        dag=dag,
    )


def email_generated_report_operator(dag, report_config):
    return PythonOperator(
        task_id='email_generated_report',
        provide_context=True,
        python_callable=email_generated_report,
        op_kwargs={
            'report_config': report_config,
        },
        dag=dag,
    )


def process_tsv_file_operator(dag, report_config):
    return PythonOperator(
        task_id='process_report_file',
        provide_context=True,
        python_callable=process_report_file,
        op_kwargs={
            'report_config': report_config,
        },
        dag=dag,
    )


def upload_to_docstore_operator(dag, report_config):
    return PythonOperator(
        task_id='upload_to_docstore',
        provide_context=True,
        python_callable=upload_to_docstore,
        op_kwargs={
            'report_config': report_config,
        },
        dag=dag,
    )


def delete_tenant_file_operator(dag, cleanup_config):
    return PythonOperator(
        task_id="delete_tenant_hdfs_file",
        python_callable=remove_files,
        op_kwargs={
            'report_config': cleanup_config,
        },
        provide_context=True,
        dag=dag,
    )


def clean_up_all_tmp_files_operator(dag, report_config):
    return PythonOperator(
        task_id='clean_up_all_tmp_files',
        provide_context=True,
        python_callable=clean_up_all_tmp_files,
        op_kwargs={
            'report_config': report_config,
        },
        dag=dag,
    )
