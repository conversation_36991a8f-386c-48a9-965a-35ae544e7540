import os
import json

from mcp_reports.ro.utils.rosey import get_rosey_config_cached

PROJECT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))

ENV = os.environ.get('ENV', 'PROD')
GLOBAL_CONFIG_FILE = 'config/prod_config.json' if ENV == 'PROD' else 'config/local_config.json'
GLOBAL_CONFIG = json.load(open(os.path.join(PROJECT_DIR, GLOBAL_CONFIG_FILE)))
REPORT_CONFIG_DIR = os.path.join(PROJECT_DIR, 'report_configs')
SFTP_CONFIG_DIR = os.path.join(PROJECT_DIR, 'sftp_configs')
REPORT_CLEANUP_CONFIG_DIR = os.path.join(PROJECT_DIR, 'report_clean_up')
QUERY_TEMPLATES_DIR = os.path.join(PROJECT_DIR, 'query_templates')
EMAIL_TEMPLATES_DIR = os.path.join(PROJECT_DIR, 'email_templates')
PYTHON_FILES_DIR = os.path.join(PROJECT_DIR, 'transformers')

ROSEY_CONFIG_PATH = os.path.join(PROJECT_DIR, GLOBAL_CONFIG['ROSEY_CONFIG_PATH'])
ROSEY_CONFIG = get_rosey_config_cached(ENV, ROSEY_CONFIG_PATH, 'mcp', 'roReports')

TEMP_HDFS_PATH = ROSEY_CONFIG['TEMP_HDFS_PATH']
TEMP_LOCAL_PATH = ROSEY_CONFIG['TEMP_LOCAL_PATH']
REALM = ROSEY_CONFIG['REALM']

OLYMPUS_PROTOCOL = ROSEY_CONFIG['OLYMPUS_PROTOCOL']
OLYMPUS_HOST = ROSEY_CONFIG['OLYMPUS_HOST']
OLYMPUS_PORT = ROSEY_CONFIG['OLYMPUS_PORT']
OLYMPUS_CLIENT_ID = ROSEY_CONFIG['OLYMPUS_CLIENT_ID']
OLYMPUS_CLIENT_KEY = ROSEY_CONFIG['OLYMPUS_CLIENT_KEY']

RO_SERVICE_URL = ROSEY_CONFIG['RO_BASE_URL']

ZENCAST_PROTOCOL = ROSEY_CONFIG['ZENCAST_PROTOCOL']
ZENCAST_HOST = ROSEY_CONFIG['ZENCAST_HOST']
ZENCAST_PORT = ROSEY_CONFIG['ZENCAST_PORT']

DOCSTORE_CONFIG = ROSEY_CONFIG['DOCSTORE_CONFIG']

DYNAMIC_REPORT_CONFIG = ROSEY_CONFIG['REPORT_CONFIG']

HDFS_USER_ID = ROSEY_CONFIG['HDFS_USER_ID']
HDFS_CMD = ROSEY_CONFIG['HDFS_CMD']
MAX_EMAIL_SIZE = 95000000
HDFS_CLEANUP_PATH = ROSEY_CONFIG.get('HDFS_CLEANUP_PATH', {})
