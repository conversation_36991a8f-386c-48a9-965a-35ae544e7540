import csv
import logging


def write_csv(lines, csv_file_path, columns):
    with open(csv_file_path, 'w') as csvfile:
        writer = csv.writer(csvfile, delimiter=',', quotechar='|', quoting=csv.QUOTE_MINIMAL)
        writer.writerow(columns)
        for line in lines:
            writer.writerow(line)


def process(tsv_file_path, csv_file_path, columns):
    with open(tsv_file_path, 'r') as tsv_file:
        reader = csv.reader(tsv_file, delimiter='\t')
        lines = list(reader)
        logging.info("Number of rows found are: {}".format(len(lines)))

        write_csv(lines, csv_file_path, columns)
