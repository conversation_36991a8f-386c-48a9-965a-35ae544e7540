import csv
import json
import logging

import requests
from requests.adapters import HTT<PERSON>dapter, Retry

from mcp_reports.ro.config.config import OLYMPUS_PROTOCOL, OLYMPUS_HOST, \
    OLYMPUS_PORT, OLYMPUS_CLIENT_ID, OLYMPUS_CLIENT_KEY
from mcp_reports.ro.config.config import RO_SERVICE_URL
from mcp_reports.ro.olympus_im_client import OlympusIMClient


def process(csv_file_path, report_config, dynamic_report_config, execution_date):
    with open(csv_file_path, 'r') as csv_file:
        url = RO_SERVICE_URL + "/v1/recon/bulk"
        olympusClient = OlympusIMClient(OLYMPUS_PROTOCOL, OLYMPUS_HOST, OLYMPUS_PORT,
                                        OLYMPUS_CLIENT_ID, OLYMPUS_CLIENT_KEY)
        reader = csv.reader(csv_file)
        lines = list(reader)
        logging.info("Number of rows found are: {}".format(len(lines)))
        s = requests.Session()
        retries = Retry(total=1,
                        backoff_factor=0.5,
                        status_forcelist=[400, 401, 402, 404, 500, 502, 503, 504],
                        method_whitelist=frozenset(['POST']))

        s.mount('https://', HTTPAdapter(max_retries=retries))
        payload = json.dumps(list(map(lambda line: line[0], lines[1:])))

        try:
            logging.info("Trigerring RO Daily recon")
            headers = {
                'content-type': 'application/json',
                'accept': 'application/json',
                'Authorization': olympusClient.get_bearer_auth_token()
            }
            res = s.post(url, headers=headers, verify=False, data=payload)
            if res.status_code // 100 == 2:
                logging.info("Recon API call completed with status_code {}".format(res.status_code))
            else:
                logging.error("RO bulk recon failed with error code ({})".format(res.status_code))
                raise Exception()
        except Exception as e:
            logging.error("RO bulk recon failed with exception {}".format(e))
            raise e
