import os
import pandas as pd
import datetime
from mcp_reports.ro.olympus_im_client import OlympusIMClient
from mcp_reports.ro.config.config import ZENCAST_PROTOCOL, ZENCAST_HOST, ZENCAST_PORT, \
    OLYMPUS_PROTOCOL, OLYMPUS_HOST, OLYMPUS_PORT, OLYMPUS_CLIENT_ID, OLYMPUS_CLIENT_KEY
import requests
import json
import mcp_reports.ro.transformers.ro_status_update as ro_status_update
import logging

def send_email(subject, message, attachment, receiver):
        logging.info("Preparing to send Email!!")

        olympusClient = OlympusIMClient(OLYMPUS_PROTOCOL, O<PERSON><PERSON>MPUS_HOST, O<PERSON>YMPUS_PORT,
                                OLYMPUS_CLIENT_ID, OLYMPUS_CLIENT_KEY)


        HEADERS = { 'accept': 'application/json',
           'Content-Type': 'application/json'}
        
        HEADERS['Authorization'] = olympusClient.get_bearer_auth_token()

        url = "{}://{}:{}/v1/communication/send/email/multicast".format(ZENCAST_PROTOCOL, ZENCAST_HOST, ZENCAST_PORT)

        receivers = ["<EMAIL>"]
        receivers.extend(receiver)
        
        try:
            response = requests.post(url=url, data=json.dumps(
                                        __build_payload_for_send_email(receivers, message, subject,attachment)),
                                        headers=HEADERS, verify=False)
            if response.status_code / 100 == 2:
                logging.info("Success send email zencast response")
                return True
            else:
                logging.info("Zencast send mail, Non success response " + str(response.status_code))
                logging.info(response.content)
        except Exception as e:
            logging.info("Unable to send email (will attempt sending email 3 times)")
            raise e
        
def __build_payload_for_send_email(receivers, message, subject, attachment):
    payload = {'requestId': 'R123', 
               'receivers': receivers, 
               'message': message, 
               'subject': subject,
               'profileId': "INTERNAL", 
               'sender': "<EMAIL>", 
               'senderName': "PhonePe",
               'attachmentsV2':attachment}

    logging.info("Prepared payload for zencast :({})".format(payload))


    logging.info("Prepared payload for zencast :({})".format(payload))

    return payload

 
def load_and_preprocess_data(file_path):
    """Load CSV data and parse dates."""
    df = pd.read_csv(file_path)
    df["created_at"] = pd.to_datetime(df["created_at"], errors="coerce")
    return df

def categorize_age(created_at):
    """Categorize transactions based on age."""
    age_days = (datetime.datetime.now() - created_at).days
    if age_days < 1:
        return "< 1 day"
    elif 1 <= age_days <= 3:
        return "1-3 days"
    elif 3 < age_days <= 5:
        return "3-5 days"
    elif 5 < age_days <= 10:
        return "5-10 days"
    else:
        return "> 10 days"

def generate_summary(df):
    """Generate a pivoted summary of transaction counts per tenant based on complex conditions with details."""

    df["age_category"] = df["created_at"].apply(categorize_age)
    df["tenant"] = df["tenant"].astype(str)
    df["status"] = df["status"].astype(str)

    # Map custom status and extract details
    def map_status_and_details(row):
        if row["status"] == "ACCEPTED" and row["current_status"] == "PAYMENT_STATE_NOT_COMPLETED":
            return "accepted_but_not_completed", row["current_status"]
        elif row["status"] == "ACCEPTED" and row["current_status"] == "COMPLETED":
            return "accepted_and_completed", row["current_status"]
        elif row["status"] == "INITIATED" and row["response_code"] == "RETRY_THRESHOLD_BREACHED":
            return "initiated_retry_threshold_breached", row["response_code"]
        elif row["status"] == "INITIATED" and row["response_code"] == "BL_052":
            return "initiated_bl_052", row["response_code"]
        else:
            return row["status"], ""

    df[["custom_status", "details"]] = df.apply(map_status_and_details, axis=1, result_type="expand")

    # Group and summarize
    summary = df.groupby(["tenant", "custom_status", "details", "age_category"]).size().reset_index(name="count")
    logging.info(summary)
    if summary.empty:
        logging.info("⚠️ Warning: Summary is empty! Check data filters.")
        return None
    else:
        return summary.pivot(index=["tenant", "custom_status", "details"], columns="age_category", values="count").fillna(0)

def combine_reasons(row):
    if pd.notna(row["response_code"]) and pd.notna(row["current_status"]):
        return f"{row['response_code']} | {row['current_status']}"
    elif pd.notna(row["response_code"]):
        return row["response_code"]
    elif pd.notna(row["current_status"]):
        return row["current_status"]
    else:
        return ""

def create_and_send_report(summary, df,dynamic_report_config):
    """Create and send individual reports for each tenant."""
    
    # Mapping of POC emails
    poc = dynamic_report_config['POC']
    poc_flag = dynamic_report_config['add_poc']

    # Getting google sheet links
    links = dynamic_report_config['GOOGLE_SHEET_LINKS']

    for tenant, tenant_data in summary.groupby(level=0):
        # Generate tenant-specific attachment
        tenant_file = f"{tenant}_transactions.csv"
        tenant_df = df[df["tenant"] == tenant]
        
        #manipulate sending report
        tenant_df["reasons"] = tenant_df.apply(combine_reasons, axis=1)
        tenant_df = tenant_df.drop(columns=["response_code", "current_status", "details","status","workflow_status"])
        tenant_df = tenant_df.rename(columns={"custom_status": "current_status"})

        tenant_df.to_csv(tenant_file, index=False)

        # Email Subject
        subject = f"RO - Stuck refunds for {tenant} | {datetime.date.today()}"

        # Email Receiver
        receiver = [poc['BLOCKED'][tenant]] if poc_flag else []
    
        # Construct Email Body
        html_message = f"""
        <p>Hi Team,</p>
        <p>Please find the refund transaction summary for <b>{tenant}</b>:</p>
        <table border="1" cellspacing="0" cellpadding="5" style="border-collapse: collapse; width: 80%;">
            <thead>
                <tr style="background-color: #f2f2f2;">
                    <th>Response State</th>
                    <th>&lt; 1 day</th>
                    <th>From 1 to 3 days</th>
                    <th>From 3 to 5 days</th>
                    <th>From 5 to 10 days</th>
                    <th>&gt; 10 days</th>
        """
        
        if poc_flag:
            html_message += """<th> POC </th>"""
        html_message += """</tr>
            </thead>
            <tbody>
        """



        for (tenant, custom_status, deatils), row in tenant_data.iterrows():
            counts = {
                "< 1 day": row.get("< 1 day", 0),
                "1-3 days": row.get("1-3 days", 0),
                "3-5 days": row.get("3-5 days", 0),
                "5-10 days": row.get("5-10 days", 0),
                "> 10 days": row.get("> 10 days", 0)
            }

            total_count = sum(counts.values())

            # Get POC Email
            poc_email = poc[custom_status] if custom_status != "BLOCKED" else poc["BLOCKED"].get(tenant, "<EMAIL>")

            # Highlighting rows where action is required
            highlight_style = "background-color: #ffcccc;" if custom_status in ["BLOCKED", "ACCEPTED"] and any(counts.values()) else ""

            highlight_style = (
                "background-color: #ffcccc;"
                if total_count > 0 and poc_email != "<EMAIL>"
                else ""
            )

            html_message += f"""
            <tr style="{highlight_style}">
                <td>{custom_status.upper()}</td>
                <td>{counts['< 1 day']}</td>
                <td>{counts['1-3 days']}</td>
                <td>{counts['3-5 days']}</td>
                <td>{counts['5-10 days']}</td>
                <td>{counts['> 10 days']}</td>
            """
            
            if poc_flag:
                html_message += """<th> POC </th>"""
                receiver.append(poc_email)
            html_message += """</tr>
                </thead>
                <tbody>
            """

        html_message += f"""
            </tbody>
        </table>
        <br>
        <p>Detailed transaction-level report is attached for <b>{tenant}</b>.</p>
        <p>Please update the attached google sheet for easy tracking of open transactions.</p>
        <a href = {links.get(tenant,'https://docs.google.com/spreadsheets/create')}> {tenant} sheet </a>
        <p>Thanks,<br>Team</p>
        """

        # Read the tenant-specific file
        with open(tenant_file, 'rb') as rd:
            txn_details = rd.read()

        attachment = [
            {
                "type": "DIRECT",
                "content": txn_details.decode("utf-8"),
                "fileName": tenant_file
            }
        ]
        logging.info(html_message)
        send_email(subject, html_message, attachment, receiver)

        # Remove temporary file after sending
        os.remove(tenant_file)

# Main Execution
def process(file_path, report_config, dynamic_report_config, execution_date):
    ro_status_update.main(file_path)
    df = load_and_preprocess_data(file_path)
    summary = generate_summary(df)
    if summary is not None:
        create_and_send_report(summary, df,dynamic_report_config)
