import csv
import logging


def read_rows(csv_file_path):
    txn_map = {}
    header_row = None
    with open(csv_file_path, 'r') as csv_file:
        reader = csv.reader(csv_file)
        lines = list(reader)
        logging.info("Number of rows found are: {}".format(len(lines)))
        header_row = lines[0]

        for line in lines[1:]:
            reference_id = line[1]
            if reference_id not in txn_map:
                txn_map[reference_id] = [line]
            else:
                line[7] = None
                line[8] = None
                line[11] = None
                txn_map[reference_id].append(line)

    return header_row, txn_map

def process(csv_file_path, report_config, dynamic_report_config, execution_date):
    header_row, txn_map = read_rows(csv_file_path)

    row_count = 0
    with open(csv_file_path, 'w') as csv_file:
        writer = csv.writer(csv_file)
        writer.writerow(header_row)
        row_count += 1
        for rows in txn_map.values():
            writer.writerows(rows)
            row_count += len(rows)

    logging.info("Number of rows written: {}".format(row_count))
