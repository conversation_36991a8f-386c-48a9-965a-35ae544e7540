import csv
import logging
import uuid

from mcp_reports.ro.utils.common import get_formatted_date, apply_template, \
    read_email_template
from mcp_reports.ro.zencast_client import ZencastClient


def send_tenant_report(rows, report_config, recipients, execution_date):
    logging.info("Initializing Zencast client")
    zencast_client = ZencastClient()

    logging.info("Generating requestId for sending email via Zencast")
    request_id = str(uuid.uuid1())

    email_config = report_config['email']
    context = {
        'EXECUTION_DATE': get_formatted_date(execution_date)
    }
    subject = email_config['subject'].format(**context)
    body = apply_template(read_email_template(email_config['templatePath']).encode(), context)
    file_name = report_config['fileName'].format(**context)
    attachment = ""
    for row in rows:
        attachment += ','.join(row) + '\n'

    logging.info(f"Sending email to({ recipients }), subject({ subject }) and requestId({ request_id }))")

    zencast_client.send_email_with_attachment(request_id, recipients, str(body), subject, file_name, attachment)


def process(csv_file_path, report_config, dynamic_report_config, execution_date):
    with open(csv_file_path, 'r') as csv_file:
        reader = csv.reader(csv_file)
        lines = list(reader)
        logging.info("Number of rows found are: {}".format(len(lines)))
        header_row = lines[0]
        lines = lines[1:]

        tenant_filters = dynamic_report_config["tenant_filters"]

        for filter in tenant_filters:
            tenants = set(filter['tenants'])
            emails = filter['emails']

            entries = [line for line in lines if line[6] in tenants]
            if len(entries) > 0:
                logging.info(f"Sending report for { ', '.join(tenants) } to { ','.join(emails) }")
                send_tenant_report([header_row] + entries, report_config, emails, execution_date)

