import requests
import json
import pandas as pd
import logging
from mcp_reports.ro.config.config import OLYMPUS_PROTOCOL, OLYMPUS_HOST, \
    OLYMPUS_PORT, OLYMPUS_CLIENT_ID, OLYMPUS_CLIENT_KEY
from mcp_reports.ro.config.config import RO_SERVICE_URL
from mcp_reports.ro.olympus_im_client import OlympusIMClient

def get_refId(input_file_path):
    df = pd.read_csv(input_file_path, sep=',')
    ref_ids = {}
    for index,row in df.iterrows():
        if row['merchant_txn_id'] not in ref_ids.keys() and row['status'] not in ('CREATED', 'INITIATED','REFUNDED'):
            ref_ids[row['merchant_txn_id']] = row['reference_id']
    return ref_ids

def call_workflow(ref_id, reversal_id, input_file, HEADERS):
    url2 = f'{RO_SERVICE_URL}/housekeeping/v1/workflow/{ref_id}'
    session = requests.Session()
    http_request = requests.adapters.HTTPAdapter(max_retries=3)
    session.mount('http://', http_request)
    response = session.get(url2, headers=HEADERS, verify=False)

    # Handle the response
    if response.status_code == 200:
        record = response.json()
        if 'REFUND_COMPLETED_DATA' in record['dataFlowInstance']['dataSet']['availableData'].keys():
            status = 'ACCEPTED'
            update_sheet(reversal_id, status, input_file)
    else:
        logging.error("Error calling the API", response.status_code, response.text)
        

    return 

def update_sheet(reversal_id, status, input_file):
    df = pd.read_csv(input_file, sep=',')
    
    for index, row in df.iterrows():
        if row['merchant_txn_id'] == reversal_id:  # Ensure you're updating the correct row
            if status == 'INITIATED':
                df.at[index, 'current_status'] = 'ALREADY MORE THAN 50 REVERSED TXNS'
            elif status == 'ACCEPTED':
                df.at[index, 'current_status'] = 'PAYMENT_STATE_NOT_COMPLETED'
            elif status == 'COMPLETED':
                df.at[index, 'current_status'] = 'COMPLETED'
            elif status == 'CANCELLED':
                df.at[index, 'current_status'] = 'CANCELLED'
    
    df.to_csv(input_file, sep=',', index=False)
    
def main(input_file):
    olympusClient = OlympusIMClient(OLYMPUS_PROTOCOL, OLYMPUS_HOST, OLYMPUS_PORT,
                                        OLYMPUS_CLIENT_ID, OLYMPUS_CLIENT_KEY)
        
    HEADERS = {
                'content-type': 'application/json',
                'accept': 'application/json',
                'Authorization': olympusClient.get_bearer_auth_token()
            }

    ref_ids = get_refId(input_file)
    for reversal_id, ref_id in ref_ids.items():
        url1 = f'{RO_SERVICE_URL}/v1/refund/{{tenantId}}/{ref_id}/status'
        session = requests.Session()
        http_request = requests.adapters.HTTPAdapter(max_retries=3)
        session.mount('http://', http_request)
        response = session.get(url1, headers=HEADERS, verify=False)

        # Handle the response
        if response.status_code == 200:
            record = response.json()
            logging.info('Success:', record['data']['status'])
            if record['data']['status'] == 'INITIATED':
                update_sheet(reversal_id,'INITIATED', input_file)
            elif record['data']['status'] == 'ACCEPTED':
                call_workflow(ref_id, reversal_id, input_file, HEADERS)
            elif record['data']['status'] == 'COMPLETED':
                update_sheet(reversal_id,'COMPLETED', input_file)
            elif record['data']['status'] == 'CANCELLED':
                update_sheet(reversal_id,'CANCELLED', input_file)
        else:
            logging.error('Error:', response.status_code, response.text)
