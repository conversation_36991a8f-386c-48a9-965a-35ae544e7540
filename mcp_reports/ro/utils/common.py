import imp
import json
import logging
import os
import subprocess

from pybars import Compiler

from mcp_reports.ro.config.config import QUERY_TEMPLATES_DIR, EMAIL_TEMPLATES_DIR, PYTHON_FILES_DIR, HDFS_USER_ID


def apply_template(template_data, template_context):
    compiler = Compiler()
    template = compiler.compile(template_data.decode())
    return ''.join(template(template_context))


def get_formatted_date(d):
    return d.format('YYYY-MM-DD')


def format_query(query, execution_date):
    context = {
        'EXECUTION_DATE': get_formatted_date(execution_date),
    }

    return apply_template(query.encode(), context)


def read_report_config(filename):
    config_filepath = os.path.join(filename)
    report_config = json.load(open(config_filepath))
    logging.info("Report config ({})".format(report_config))
    return report_config


def read_query_template(query_template_filename):
    query_template_filepath = os.path.join(QUERY_TEMPLATES_DIR, query_template_filename)
    return open(query_template_filepath).read()


def read_email_template(email_template_filename):
    email_template_filepath = os.path.join(EMAIL_TEMPLATES_DIR, email_template_filename)
    return open(email_template_filepath).read()


def create_dir_for_file(file_path):
    dir_path = os.path.dirname(file_path)
    logging.info("Creating directory for path {}".format(dir_path))
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)
        logging.info("Created directory for path {}".format(dir_path))


def run_cmd(cmd):
    logging.info("Running subprocess: cmd({})".format(cmd))
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True)
    stdout, _ = process.communicate()
    return_code = process.returncode
    if return_code == 0:
        logging.info("Running subprocess completed: stdout({})".format(stdout))
        return stdout
    else:
        raise Exception("Running subprocess failed: return_code({}), stdout({})".format(return_code, stdout))


def run_cmd_with_exception(cmd):
    logging.info("Running subprocess with handling exception : cmd({})".format(cmd))
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True)
    stdout, _ = process.communicate()
    return_code = process.returncode
    if return_code == 0:
        logging.info("Running subprocess completed: stdout({})".format(stdout))
        return stdout
    else:
        logging.error("Running subprocess failed: return_code({}), stdout({})".format(return_code, stdout))


def append_to_file_top(file_path, content):
    cmd = "echo '{content}' | cat - '{file_path}' > '{temp_path}' && mv '{temp_path}' '{file_path}'".format(
        content=content,
        file_path=file_path,
        temp_path="{}.swp".format(file_path)
    )
    run_cmd(cmd)


def remove_file_pattern(file_path_pattern):
    cmd = "rm -rf {file_path_pattern}".format(file_path_pattern=file_path_pattern)
    run_cmd_with_exception(cmd)


def remove_file_pattern_using_airflow_user(file_path_pattern):
    cmd = "sudo -u {airflow_user} rm -rf {file_path_pattern}".format(airflow_user=HDFS_USER_ID,
                                                                     file_path_pattern=file_path_pattern)
    run_cmd_with_exception(cmd)


def create_dirs_for_user(dir_path):
    cmd = "sudo -u {airflow_user} mkdir -p {dir_path}".format(airflow_user=HDFS_USER_ID, dir_path=dir_path)
    run_cmd(cmd)


def chmod_for_user(dir_path):
    cmd = "sudo -u {airflow_user} chmod -R 777 {dir_path}".format(airflow_user=HDFS_USER_ID, dir_path=dir_path)
    run_cmd_with_exception(cmd)


def line_count(file_path):
    with open(file_path, 'r') as f:
        return len(f.readlines())


def write_units_to_file(tsv_file_path, csv_file_path, python_file_name, execution_date):
    logging.info("python file path is {}".format(PYTHON_FILES_DIR + "/" + python_file_name))
    processor = imp.load_source("", PYTHON_FILES_DIR + "/" + python_file_name)
    processor.process(tsv_file_path, csv_file_path, execution_date)


def process_file(tsv_file_path, temp_csv_local_path, python_file_name, csv_header_columns):
    logging.info("python file path is {}".format(PYTHON_FILES_DIR + "/" + python_file_name))
    processor = imp.load_source("", PYTHON_FILES_DIR + "/" + python_file_name)
    processor.process(tsv_file_path, temp_csv_local_path, csv_header_columns)

def process_report(temp_csv_local_path, report_config, dynamic_report_config, execution_date):
    logging.info("python file path is {}".format(PYTHON_FILES_DIR + "/" + report_config['pythonFile']))
    processor = imp.load_source("", PYTHON_FILES_DIR + "/" + report_config['pythonFile'])
    processor.process(temp_csv_local_path, report_config, dynamic_report_config, execution_date)


def convert_tsv_to_custom_csv(tsv_file_path, csv_file_path, python_file_name, execution_date):
    write_units_to_file(tsv_file_path, csv_file_path, python_file_name, execution_date)


def determine_execution_date(**kwargs):
    if kwargs['execution_date'].second != 0:
        # Manual run
        execution_date = kwargs['prev_execution_date']
    else:
        # Scheduled or backfill run
        execution_date = kwargs['next_execution_date']

    logging.info("execution date: {}".format(execution_date))
    return execution_date
