import json
import logging
import requests

from datetime import datetime

from mcp_reports.ro.olympus_im_client import OlympusIMClient
from mcp_reports.ro.config.config import DOCSTORE_CONFIG, \
    OLYMPUS_PROTOCOL, OLYMPUS_HOST, OLYMPUS_PORT, OLYMPUS_CLIENT_ID, OLYMPUS_CLIENT_KEY

DEFAULT_ALIAS_ID = DOCSTORE_CONFIG['DEFAULT_ALIAS_ID']
NAMESPACE = DOCSTORE_CONFIG['DOCSTORE_NAMESPACE']
DOCSTORE_URL = DOCSTORE_CONFIG['DOCSTORE_URL']
DOCSTORE_UI_URL = DOCSTORE_CONFIG['DOCSTORE_UI_URL']
DOCSTORE_BASE_URL = DOCSTORE_CONFIG['DOCSTORE_BASE_URL']

def get_request_headers():
    olympus_client = OlympusIMClient(OLYMPUS_PROTOCOL, OLYMP<PERSON>_HOST, OLYMPUS_PORT,
                                     <PERSON>LYMP<PERSON>_CLIENT_ID, OLYMPUS_CLIENT_KEY)
    return {
        "Authorization": olympus_client.get_bearer_auth_token()
    }


def upload_file(namespace, file_path, checksum=None):
    with open(file_path, 'r') as f:
        logging.info("Uploading to docstore: namespace({}), file_path({}), lines({})".format(namespace, file_path, len(f.readlines())))

    url = "{}/v1/documents/{}".format(DOCSTORE_URL, namespace)
    params = {
        'file': open(file_path, 'rb'),
    }
    if checksum:
        url = "{}/v2/documents/{}".format(DOCSTORE_URL, namespace)
        params['fileUploadRequest'] = json.dumps({
            'md5Checksum': str(checksum),
            'fileUploadContext': {
                'type': 'INTERNAL',
            }
        })

    try:
        response = requests.post(url=url, files=params, headers=get_request_headers(), verify=False)
        if 200 == response.status_code:
            logging.info("Uploading to docstore success: response({})".format(response.json()))
            return response.json()
        else:
            logging.error("Uploading to docstore failed: status({}), response({})".format(response.status_code, response.text))
    except Exception as e:
        logging.error("Failed to upload. Exception: {}".format(e))
        raise e

def add_to_docstore(namespace, file_path, alias_id, checksum=None):
    logging.info("add_to_docstore {} {} {}".format(namespace, file_path, alias_id))
    file_id = upload_file(namespace, file_path, checksum)['context']['id']
    add_files_to_alias(alias_id, [file_id])
    return file_id


def add_files_to_alias(alias_id, file_ids):
    logging.info("Adding files to alias: alias_id({}), file_ids({})".format(alias_id, file_ids))
    payload = {
        "fileIds": file_ids
    }
    url = '{}/v1/alias/metas/add/{}'.format(DOCSTORE_URL, alias_id)
    headers = get_request_headers()
    headers["Content-Type"] = "application/json"
    payload = json.dumps(payload)
    response = requests.put(url, data=payload, headers=headers, verify=False)
    if 200 == response.status_code:
        logging.info("Adding files to alias success: response({})".format(response.json()))
        return response.json()
    else:
        raise Exception("Adding files to alias failed: status({}), response({})".format(response.status_code, response.text))


def download_link(doc_id):
    return "{}/downloadFile?docId={}".format(DOCSTORE_UI_URL, doc_id)


def get_alias_id(alias_name):
    docstore = search_alias()
    dir_name = []
    for data in docstore:
        if alias_name == data['aliasName']:
            alias_id = data['id']
            logging.info("alias_name: {} | alias_id: {}".format(alias_name, alias_id))
            return alias_id
        dir_name.append(data['aliasName'])
    logging.info("dir_name: ".format(dir_name))
    if alias_name not in dir_name:
        alias_id = create_alias(NAMESPACE, alias_name)
        logging.info("alias_name: {} | alias_id: {}".format(alias_name, alias_id))
        return alias_id['context']['id']
    else:
        details = get_alias(DEFAULT_ALIAS_ID)
        logging.info("default alias_name: {} | default alias_id: {}".format(details['aliasName'], DEFAULT_ALIAS_ID))
        return DEFAULT_ALIAS_ID


def create_alias(namespace, alias_name):
    url = DOCSTORE_BASE_URL + "/v1/alias/{namespace}".format(namespace=namespace)
    payload = {
        "aliasName": alias_name,
        "Tags": "ro",
        "author": "ro"
    }
    response = requests.post(url=url, json=payload, headers=get_request_headers(), verify=False)
    if 200 == response.status_code:
        logging.info("Creation of alias success: response({})".format(response.json()))
        return response.json()
    else:
        raise Exception("creation of alias failed: status({}), response({})".format(response.status_code, response.text))


def search_alias():
    url = DOCSTORE_BASE_URL + "/v1/alias/search/{namespace}".format(namespace=NAMESPACE)
    response = requests.get(url=url, headers=get_request_headers(), verify=False)
    if 200 == response.status_code:
        logging.info("Alias search for namespace {} returned {} results".format(NAMESPACE, len(response.json())))
        return response.json()
    else:
        raise Exception("Namespace alias search failed: status({}), response({})".format(response.status_code, response.text))


def get_alias(alias_id):
    url = DOCSTORE_BASE_URL + "/v1/alias/{alias_id}".format(alias_id=alias_id)
    response = requests.get(url=url, headers=get_request_headers(), verify=False)
    if 200 == response.status_code:
        return response.json()
    else:
        raise Exception("Alias search failed for {}: status({}), response({})".format(alias_id, response.status_code, response.text))


def docstore_links(alias, files):
    html = ""
    link = {}
    get_alias = get_alias_id(alias)
    for file_path in files:
        file_name = file_path.split('/')[-1]
        try:
            logging.info("Generating docstore link for: {}".format(file_path))
            file_id = add_to_docstore(NAMESPACE, file_path, get_alias)
            file_link = download_link(file_id)
            link[file_name] = file_link
        except Exception as e:
            logging.error("Unable to generate file link for {}: {}".format(file_path, e))
    for key, value in link.items():
        html += "<p>{} : {} <br /> <p>".format(key, value)
    return html