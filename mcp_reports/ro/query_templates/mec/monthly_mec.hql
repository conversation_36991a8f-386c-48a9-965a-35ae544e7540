select
    payer_id,
    reference_id,
    merchant_id,
    merchant_txn_id,
    merchant_forward_txn_id,
    max(payment_txn_id) as payment_txn_id,
    payment_forward_txn_id,
    max(amount) as amount,
    max(total_amount) as total_amount,
    max(refund_state) as refund_state,
    max(workflow_state) as workflow_state,
    tenant,
    max(created_at) as created_at,
    max(completed_date) as completed_date
from (
    select
        coalesce(ra.payer_id, rf.payer_id) as payer_id,
        coalesce(ra.reference_id, rf.reference_id) as reference_id,
        coalesce(ra.merchant_id, rf.merchant_id) as merchant_id,
        coalesce(ra.merchant_txn_id, rf.merchant_txn_id) as merchant_txn_id,
        coalesce(ra.merchant_forward_txn_id, rf.merchant_forward_txn_id) as merchant_forward_txn_id,
        coalesce(rf.payment_txn_id, ra.payment_txn_id) as payment_txn_id,
        coalesce(ra.payment_forward_txn_id, rf.payment_forward_txn_id) as payment_forward_txn_id,
        coalesce(ra.amount, rf.amount) as amount,
        coalesce(ra.total_amount, rf.total_amount) as total_amount,
        coalesce(rf.status, ra.status) as refund_state,
        wdi.workflow_state as workflow_state,
        coalesce(ra.tenant, rf.tenant) as tenant,
        coalesce(rf.created_at, ra.created_at) as created_at,
        date(from_unixtime(cast(get_json_object(coalesce(rf.backend_error, ra.backend_error), "$.PAYMENT_COMPLETED_AT") / 1000 as bigint) + 19800)) as completed_date
    from refund_orchestrator.refund_audit ra
    full join refund_orchestrator.refund rf on ra.reference_id = rf.reference_id
    full join refund_orchestrator.workflow_data_instances wdi on ra.reference_id = wdi.workflow_id
    cross join (
        select
           (12 + month(current_date) - 2) % 12 + 1 as mec_month,
           if (month(current_date) = 1, year(current_date) - 1, year(current_date)) as mec_year
    ) mecr
    where
        (ra.type in ('ADJUSTMENT', 'REVERSAL') or rf.type in ('ADJUSTMENT', 'REVERSAL'))
        and (ra.year = mecr.mec_year or rf.year = mecr.mec_year)
        and (ra.month = mecr.mec_month or rf.month = mecr.mec_month)
) t
group by
    payer_id,
    reference_id,
    merchant_id,
    merchant_txn_id,
    merchant_forward_txn_id,
    payment_forward_txn_id,
    tenant
order by created_at