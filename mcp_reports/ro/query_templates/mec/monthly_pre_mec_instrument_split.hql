select
    mec.payer_id as payer_id,
    mec.reference_id as reference_id,
    mec.merchant_id as merchant_id,
    mec.merchant_txn_id as merchant_txn_id,
    mec.merchant_forward_txn_id as merchant_forward_txn_id,
    mec.payment_txn_id as payment_txn_id,
    mec.payment_forward_txn_id as payment_forward_txn_id,
    mec.amount as total_base_amount,
    mec.total_amount as total_amount,
    ins.amount as instrument_split_amount,
    ins.type as instrument_type,
    mec.refund_state as refund_state,
    mec.workflow_state as workflow_state,
    mec.tenant as tenant,
    mec.created_at as created_at,
    mec.completed_date as completed_date
from (
    select
        t.payer_id,
        t.reference_id,
        t.merchant_id,
        t.merchant_txn_id,
        t.merchant_forward_txn_id,
        max(t.payment_txn_id) as payment_txn_id,
        t.payment_forward_txn_id,
        max(t.amount) as amount,
        max(t.total_amount) as total_amount,
        max(t.refund_state) as refund_state,
        max(t.workflow_state) as workflow_state,
        t.tenant,
        max(t.created_at) as created_at,
        max(t.completed_date) as completed_date,
        max(t.year) as year,
        max(t.month) as month
    from (
        select
            coalesce(ra.payer_id, rf.payer_id) as payer_id,
            coalesce(ra.reference_id, rf.reference_id) as reference_id,
            coalesce(ra.merchant_id, rf.merchant_id) as merchant_id,
            coalesce(ra.merchant_txn_id, rf.merchant_txn_id) as merchant_txn_id,
            coalesce(ra.merchant_forward_txn_id, rf.merchant_forward_txn_id) as merchant_forward_txn_id,
            coalesce(rf.payment_txn_id, ra.payment_txn_id) as payment_txn_id,
            coalesce(ra.payment_forward_txn_id, rf.payment_forward_txn_id) as payment_forward_txn_id,
            coalesce(ra.amount, rf.amount) as amount,
            coalesce(ra.total_amount, rf.total_amount) as total_amount,
            coalesce(rf.status, ra.status) as refund_state,
            wdi.workflow_state as workflow_state,
            coalesce(ra.tenant, rf.tenant) as tenant,
            coalesce(rf.created_at, ra.created_at) as created_at,
            date(from_unixtime(cast(get_json_object(coalesce(rf.backend_error, ra.backend_error), "$.PAYMENT_COMPLETED_AT") / 1000 as bigint) + 19800)) as completed_date,
            mecr.mec_year as year,
            mecr.mec_month as month
        from refund_orchestrator.refund_audit ra
        full join refund_orchestrator.refund rf on ra.reference_id = rf.reference_id
        full join refund_orchestrator.workflow_data_instances wdi on ra.reference_id = wdi.workflow_id
        cross join (
            select
                month(current_date) as mec_month,
                year(current_date) as mec_year
        ) mecr
        where
            (ra.type in ('ADJUSTMENT', 'REVERSAL') or rf.type in ('ADJUSTMENT', 'REVERSAL'))
            and (year(ra.payment_completed_at) = mecr.mec_year or year(rf.payment_completed_at) = mecr.mec_year)
            and (month(ra.payment_completed_at) = mecr.mec_month or month(rf.payment_completed_at) = mecr.mec_month)
    ) t
    group by
        payer_id,
        reference_id,
        merchant_id,
        merchant_txn_id,
        merchant_forward_txn_id,
        payment_forward_txn_id,
        tenant
) mec
left join payment.transaction_receiver_instruments ins
    on mec.payment_txn_id = ins.transaction_id
    and ins.year = mec.year
    and ins.month = mec.month
order by mec.created_at
