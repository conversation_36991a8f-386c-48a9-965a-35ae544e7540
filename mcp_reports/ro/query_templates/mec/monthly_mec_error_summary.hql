SELECT 
    transaction_date, 
    SUM(event_count) AS event_count,
    event_ingestion_HTTP_status,
    event_ingestion_response_entity_code,
    transaction_status_HTTP_status
FROM (
    SELECT * FROM (SELECT
        transaction_date,
        COUNT(*) AS event_count,
        COALESCE(accounting_event_status, 200) AS event_ingestion_HTTP_status,
        COALESCE(accounting_event_response, 'NA') AS event_ingestion_response_entity_code,
        COALESCE(accounting_status, 200) AS transaction_status_HTTP_status
    FROM ( SELECT 
        date_format(from_unixtime(unix_timestamp(created_at)), 'dd/MM/yyyy') AS transaction_date,
        get_json_object(ingestion_info, '$.accountingEventIngestionInfo.statusCode') AS accounting_event_status,
        get_json_object(ingestion_info, '$.accountingEventIngestionInfo.responseCode') AS accounting_event_response,
        get_json_object(ingestion_info, '$.entryUpdateIngestionInfo.statusCode') AS entry_update_status,
        get_json_object(ingestion_info, '$.entryUpdateIngestionInfo.responseCode') AS entry_update_response,
        get_json_object(status_info, '$.accountingEventStatusInfo.statusCode') AS accounting_status,
        get_json_object(status_info, '$.entryUpdateStatusInfo.statusCode') AS entry_update_status_info
    FROM refund_orchestrator.accounting_events) as extracted_data
    GROUP BY transaction_date, accounting_event_status, accounting_event_response, accounting_status) as u1
    UNION ALL
    SELECT * FROM (SELECT
        transaction_date,
        COUNT(*) AS event_count,
        COALESCE(entry_update_status, 200) AS event_ingestion_HTTP_status,
        COALESCE(entry_update_response, 'NA') AS event_ingestion_response_entity_code,
        COALESCE(entry_update_status_info, 200) AS transaction_status_HTTP_status
    FROM ( SELECT 
        date_format(from_unixtime(unix_timestamp(created_at)), 'dd/MM/yyyy') AS transaction_date,
        get_json_object(ingestion_info, '$.accountingEventIngestionInfo.statusCode') AS accounting_event_status,
        get_json_object(ingestion_info, '$.accountingEventIngestionInfo.responseCode') AS accounting_event_response,
        get_json_object(ingestion_info, '$.entryUpdateIngestionInfo.statusCode') AS entry_update_status,
        get_json_object(ingestion_info, '$.entryUpdateIngestionInfo.responseCode') AS entry_update_response,
        get_json_object(status_info, '$.accountingEventStatusInfo.statusCode') AS accounting_status,
        get_json_object(status_info, '$.entryUpdateStatusInfo.statusCode') AS entry_update_status_info
    FROM refund_orchestrator.accounting_events) as extracted_data
    WHERE entry_update_status is not null or entry_update_status_info is not null
    GROUP BY transaction_date, entry_update_status, entry_update_response, entry_update_status_info) as u2
) AS union_events
GROUP BY 
    transaction_date, 
    event_ingestion_HTTP_status,
    event_ingestion_response_entity_code,
    transaction_status_HTTP_status
ORDER BY 
    transaction_date DESC;
