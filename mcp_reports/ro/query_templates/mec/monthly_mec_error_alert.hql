
SELECT 
    transaction_date,
    transaction_id,
    event_type,
    amount,
    mid,
    COALESCE(accounting_event_status, 200) AS event_ingestion_HTTP_status,
    COALESCE(accounting_event_response, 'NA') AS event_ingestion_response_entity_code,
    COALESCE(accounting_status, 200) AS transaction_status_HTTP_status
FROM 
    (SELECT 
        date_format(from_unixtime(unix_timestamp(created_at)), 'dd/MM/yyyy') AS transaction_date,
        payment_txn_id AS transaction_id,
        get_json_object(data, '$.transaction.amount') AS amount,
        merchant_id AS mid,
        event_type AS event_type,
        get_json_object(ingestion_info, '$.accountingEventIngestionInfo.statusCode') AS accounting_event_status,
        get_json_object(ingestion_info, '$.accountingEventIngestionInfo.responseCode') AS accounting_event_response,
        get_json_object(ingestion_info, '$.entryUpdateIngestionInfo.statusCode') AS entry_update_status,
        get_json_object(ingestion_info, '$.entryUpdateIngestionInfo.responseCode') AS entry_update_response,
        get_json_object(status_info, '$.accountingEventStatusInfo.statusCode') AS accounting_status,
        get_json_object(status_info, '$.entryUpdateStatusInfo.statusCode') AS entry_update_status_info
    FROM refund_orchestrator.accounting_events) as extracted_data
WHERE 
    accounting_status IS NOT NULL or accounting_event_status is NOT NULL
UNION ALL
SELECT 
    transaction_date,
    transaction_id,
    'ENTRY_UPDATE' AS event_type,
    amount,
    mid,
    COALESCE(entry_update_status, 200) AS event_ingestion_HTTP_status,
    COALESCE(entry_update_response, 'NA') AS event_ingestion_response_entity_code,
    entry_update_status_info AS transaction_status_HTTP_status
FROM 
    (SELECT 
        date_format(from_unixtime(unix_timestamp(created_at)), 'dd/MM/yyyy') AS transaction_date,
        payment_txn_id AS transaction_id,
        get_json_object(data, '$.transaction.amount') AS amount,
        merchant_id AS mid,
        event_type AS event_type,
        get_json_object(ingestion_info, '$.accountingEventIngestionInfo.statusCode') AS accounting_event_status,
        get_json_object(ingestion_info, '$.accountingEventIngestionInfo.responseCode') AS accounting_event_response,
        get_json_object(ingestion_info, '$.entryUpdateIngestionInfo.statusCode') AS entry_update_status,
        get_json_object(ingestion_info, '$.entryUpdateIngestionInfo.responseCode') AS entry_update_response,
        get_json_object(status_info, '$.accountingEventStatusInfo.statusCode') AS accounting_status,
        get_json_object(status_info, '$.entryUpdateStatusInfo.statusCode') AS entry_update_status_info
    FROM refund_orchestrator.accounting_events) as extracted_data
WHERE 
    entry_update_status is not null or entry_update_status_info is not null
ORDER BY 
    transaction_date DESC;

 