select
    payer_id,
    reference_id,
    merchant_id,
    merchant_txn_id,
    merchant_forward_txn_id,
    payment_txn_id,
    payment_forward_txn_id,
    case when row_number = 1 then total_base_amount else null end as total_base_amount,
    case when row_number = 1 then total_amount else null end as total_amount,
    instrument_split_amount,
    instrument_type,
    case when row_number = 1 then original_txn_amount else null end as original_txn_amount,
    refund_state,
    workflow_state,
    tenant,
    type,
    created_at,
    completed_date
from (
    select
        mec.payer_id as payer_id,
        mec.reference_id as reference_id,
        mec.merchant_id as merchant_id,
        mec.merchant_txn_id as merchant_txn_id,
        mec.merchant_forward_txn_id as merchant_forward_txn_id,
        mec.payment_txn_id as payment_txn_id,
        mec.payment_forward_txn_id as payment_forward_txn_id,
        mec.amount as total_base_amount,
        mec.total_amount as total_amount,
        instruments.amount as instrument_split_amount,
        instruments.type as instrument_type,
        mec.original_txn_amount as original_txn_amount,
        mec.refund_state as refund_state,
        mec.workflow_state as workflow_state,
        mec.tenant as tenant,
        mec.type as type,
        mec.created_at as created_at,
        mec.completed_date as completed_date,
        ROW_NUMBER() OVER (PARTITION BY mec.payment_txn_id ORDER BY instruments.amount DESC) as row_number
    from (
        select
            t.payer_id,
            t.reference_id,
            t.merchant_id,
            t.merchant_txn_id,
            t.merchant_forward_txn_id,
            max(t.payment_txn_id) as payment_txn_id,
            t.payment_forward_txn_id,
            max(t.amount) as amount,
            max(t.total_amount) as total_amount,
            max(t.original_txn_amount) as original_txn_amount,
            max(t.refund_state) as refund_state,
            max(t.workflow_state) as workflow_state,
            t.tenant,
            t.type,
            max(t.created_at) as created_at,
            max(t.completed_date) as completed_date,
            max(t.year) as year,
            max(t.month) as month
        from (
            select
                coalesce(ra.payer_id, rf.payer_id) as payer_id,
                coalesce(ra.reference_id, rf.reference_id) as reference_id,
                coalesce(ra.merchant_id, rf.merchant_id) as merchant_id,
                coalesce(ra.merchant_txn_id, rf.merchant_txn_id) as merchant_txn_id,
                coalesce(ra.merchant_forward_txn_id, rf.merchant_forward_txn_id) as merchant_forward_txn_id,
                coalesce(rf.payment_txn_id, ra.payment_txn_id) as payment_txn_id,
                coalesce(ra.payment_forward_txn_id, rf.payment_forward_txn_id) as payment_forward_txn_id,
                coalesce(ra.amount, rf.amount) as amount,
                coalesce(ra.total_amount, rf.total_amount) as total_amount,
                coalesce(ra.original_txn_amount, rf.original_txn_amount) as original_txn_amount,
                coalesce(rf.status, ra.status) as refund_state,
                wdi.workflow_state as workflow_state,
                coalesce(ra.tenant, rf.tenant) as tenant,
                coalesce(ra.type, rf.type) as type,
                coalesce(rf.created_at, ra.created_at) as created_at,
                date(from_unixtime(cast(get_json_object(coalesce(rf.backend_error, ra.backend_error), "$.PAYMENT_COMPLETED_AT") / 1000 as bigint) + 19800)) as completed_date,
                mecr.year as year,
                mecr.month as month
            from refund_orchestrator.refund_audit ra
            full join refund_orchestrator.refund rf on ra.reference_id = rf.reference_id
            full join refund_orchestrator.workflow_data_instances wdi on ra.reference_id = wdi.workflow_id
            cross join (
                select
                    (12 + month(current_date) - 2) % 12 + 1 as month,
                    if (month(current_date) = 1, year(current_date) - 1, year(current_date)) as year
            ) mecr
            where
                (ra.type in ('ADJUSTMENT', 'REVERSAL') or rf.type in ('ADJUSTMENT', 'REVERSAL'))
                and (year(ra.payment_completed_at) = mecr.year or year(rf.payment_completed_at) = mecr.year)
                and (month(ra.payment_completed_at) = mecr.month or month(rf.payment_completed_at) = mecr.month)
        ) t
        group by
            t.payer_id,
            t.reference_id,
            t.merchant_id,
            t.merchant_txn_id,
            t.merchant_forward_txn_id,
            t.payment_forward_txn_id,
            t.tenant,
            t.type
    ) mec
    left join (
        select tri.transaction_id,
            sum(tri.amount) as amount,
            tri.type
        from payment.transaction_receiver_instruments tri
        inner join (
            select distinct
                coalesce(ra.payment_txn_id, rf.payment_txn_id) as payment_txn_id,
                mecd.year,
                mecd.month
            from refund_orchestrator.refund_audit ra
            full join refund_orchestrator.refund rf on ra.reference_id = rf.reference_id
            cross join (
                select
                    (12 + month(current_date) - 2) % 12 + 1 as month,
                    if (month(current_date) = 1, year(current_date) - 1, year(current_date)) as year
            ) mecd
            where
                (ra.type in ('ADJUSTMENT', 'REVERSAL') or rf.type in ('ADJUSTMENT', 'REVERSAL'))
                and (year(ra.payment_completed_at) = mecd.year or year(rf.payment_completed_at) = mecd.year)
                and (month(ra.payment_completed_at) = mecd.month or month(rf.payment_completed_at) = mecd.month)
        ) ro_txns
            on ro_txns.payment_txn_id = tri.transaction_id
            and tri.year = ro_txns.year
            and tri.month = ro_txns.month
        group by
            tri.transaction_id,
            tri.type

        union all

        select toa.transaction_id,
            sum(cast(toa.amount as bigint)) as amount,
            'OFFER' as type
        from payment.transaction_offer_adjustments toa
        inner join (
            select distinct
                coalesce(ra.payment_txn_id, rf.payment_txn_id) as payment_txn_id,
                mecd.year,
                mecd.month
            from refund_orchestrator.refund_audit ra
            full join refund_orchestrator.refund rf on ra.reference_id = rf.reference_id
            cross join (
                select
                    (12 + month(current_date) - 2) % 12 + 1 as month,
                    if (month(current_date) = 1, year(current_date) - 1, year(current_date)) as year
            ) mecd
            where
                (ra.type in ('ADJUSTMENT', 'REVERSAL') or rf.type in ('ADJUSTMENT', 'REVERSAL'))
                and (year(ra.payment_completed_at) = mecd.year or year(rf.payment_completed_at) = mecd.year)
                and (month(ra.payment_completed_at) = mecd.month or month(rf.payment_completed_at) = mecd.month)
        ) ro_txns
            on ro_txns.payment_txn_id = toa.transaction_id
            and toa.year = ro_txns.year
            and toa.month = ro_txns.month
        group by
            toa.transaction_id
    ) instruments on mec.payment_txn_id = instruments.transaction_id
    group by
        mec.payer_id,
        mec.reference_id,
        mec.merchant_id,
        mec.merchant_txn_id,
        mec.merchant_forward_txn_id,
        mec.payment_txn_id,
        mec.payment_forward_txn_id,
        mec.amount,
        mec.total_amount,
        instruments.amount,
        instruments.type,
        mec.refund_state,
        mec.workflow_state,
        mec.tenant,
        mec.type,
        mec.original_txn_amount,
        mec.created_at,
        mec.completed_date
) data
order by payment_txn_id, created_at