SELECT
    wdi.workflow_id as workflow_id,
    r.status as refund_status,
    r.type as type,
    wdi.workflow_state as workflow_state,
    wdi.created_at as workflow_created_at,
    wdi.updated_at as workflow_updated_at,
    r.tenant as tenant,
    r.merchant_id as merchant_id,
    r.merchant_txn_id as merchant_txn_id,
    r.merchant_forward_txn_id as merchant_fwd_txn_id,
    r.payment_txn_id as payment_txn_id,
    r.payment_forward_txn_id as payment_fwd_txn_id,
    r.amount as amount,
    r.total_amount as total_amount,
    get_json_object(r.backend_error, '$.RESPONSE_CODE') as response_code,
    get_json_object(r.backend_error, '$.PAYMENT_RETRY_COUNT') as payment_retry_count
FROM refund_orchestrator.workflow_data_instances wdi
LEFT JOIN refund_orchestrator.refund r ON wdi.workflow_id = r.reference_id
CROSS JOIN (
    select
        (12 + month(current_date) - 2) % 12 + 1 as month,
        if (month(current_date) = 1, year(current_date) - 1, year(current_date)) as year
) rec
WHERE
    (wdi.workflow_state IN ('INITIATED', 'ACCEPTED', 'REFUNDED','CREATED'))
    AND wdi.created_at < current_date
    AND wdi.year >= rec.year
    AND wdi.month >= rec.month
ORDER BY workflow_state, refund_status, workflow_created_at