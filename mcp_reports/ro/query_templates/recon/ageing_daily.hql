select
wdi.created_at as created_at,
re.tenant as tenant,
re.merchant_id as merchant_id,
re.merchant_txn_id as merchant_txn_id,
wdi.workflow_id as reference_id,
re.payment_txn_id as payment_txn_id,
re.payment_forward_txn_id as payment_forward_txn_id,
re.amount as amount,
re.status as status,
wdi.workflow_state as workflow_status,
get_json_object(re.backend_error, '$.RESPONSE_CODE') as response_code
FROM 
refund_orchestrator.workflow_data_instances wdi
LEFT JOIN 
refund_orchestrator.refund re ON wdi.workflow_id = re.reference_id
where
wdi.workflow_state IN ('ACCEPTED','CREATED','INITIATED','REFUNDED','APPROVAL') 
AND re.status NOT IN ('COMPLETED','FAILED')
AND wdi.created_at < date_add(current_date, -2)
AND wdi.created_at >= date_add(current_date(),-3)
ORDER BY wdi.created_at desc
