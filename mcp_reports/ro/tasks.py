import logging
import traceback
import uuid

from hive.hook.secured_hive_cli_hook import Secured<PERSON><PERSON><PERSON><PERSON><PERSON>ook

from mcp_reports.ro.config.config import HDFS_USER_ID, \
    REALM, HDFS_CLEANUP_PATH, DYNAMIC_REPORT_CONFIG, TEMP_HDFS_PATH, TEMP_LOCAL_PATH
from mcp_reports.ro.utils.common import read_query_template, format_query, \
    get_formatted_date, process_file, process_report, apply_template, \
    read_email_template, remove_file_pattern_using_airflow_user, line_count, determine_execution_date
from mcp_reports.ro.utils.docstore import docstore_links
from mcp_reports.ro.utils.hdfs import delete_from_hdfs, create_hdfs_dir, concat_hdfs_dir_to_local, \
    put_local_path_to_hdfs_dir, get_hdfs_path_to_local_dir
from mcp_reports.ro.zencast_client import ZencastClient

TSV_PROCESSOR = "tsv_to_csv.py"

def get_file_paths(report_name, file_name, execution_date, query):
    query = ".".join(query.split("/")[-1].split(".")[:-1]) if query is not None else query
    temp_hdfs_dir = TEMP_HDFS_PATH.format(REPORT_NAME=report_name,
                                          EXECUTION_DATE=get_formatted_date(execution_date),
                                          QUERY=query)
    temp_local_dir = TEMP_LOCAL_PATH.format(REPORT_NAME=report_name,
                                            EXECUTION_DATE=get_formatted_date(execution_date),
                                            QUERY=query)
    file_name = file_name.format(REPORT_NAME=report_name,
                                 EXECUTION_DATE=get_formatted_date(execution_date),
                                 QUERY=query)
    temp_tsv_local_path = temp_local_dir + file_name + ".tsv"
    temp_csv_local_path = temp_local_dir + file_name + ".csv"
    temp_csv_hdfs_path = temp_hdfs_dir + file_name + ".csv"

    logging.info("temp_hdfs_path: " + temp_hdfs_dir)
    logging.info("temp_local_path: " + temp_local_dir)
    logging.info("temp_tsv_local_path: " + temp_tsv_local_path)
    logging.info("temp_csv_local_path: " + temp_csv_local_path)
    logging.info("temp_csv_hdfs_path: " + temp_csv_hdfs_path)

    return temp_hdfs_dir, temp_local_dir, temp_tsv_local_path, temp_csv_local_path, temp_csv_hdfs_path


def execute_hive_query(report_config, **kwargs):
    execution_date = determine_execution_date(**kwargs)
    for template_path in report_config['query']['templatePath']:
        query_template = read_query_template(template_path)
        temp_hdfs_dir, temp_local_dir, temp_tsv_local_path, temp_csv_local_path, temp_csv_hdfs_path = get_file_paths(
            report_config['name'],
            report_config['fileName'],
            execution_date, template_path)

        hql = format_query(query_template, execution_date)
        hql = """
        set tez.queue.name = default;
        set hive.exec.compress.output=false; 
        set hive.merge.tezfiles=true;
        INSERT OVERWRITE DIRECTORY '{report_directory}' ROW FORMAT DELIMITED FIELDS TERMINATED BY '\t' STORED AS TEXTFILE
        """.format(report_directory=temp_hdfs_dir) + hql
        hive_hook = SecuredHiveCliHook(user=HDFS_USER_ID, realm=REALM)

        logging.info("Hql query ({})".format(hql))

        create_hdfs_dir(temp_hdfs_dir)
        hive_hook.run_cli(hql)


def get_report(temp_csv_hdfs_path, temp_local_dir):
    logging.info("Copying csv report from hdfs: {} to local:{}".format(temp_csv_hdfs_path, temp_local_dir))
    get_hdfs_path_to_local_dir(temp_csv_hdfs_path, temp_local_dir)
    temp_csv_local_path = "/".join([temp_local_dir, temp_csv_hdfs_path.split("/")[-1]])
    line_count(temp_csv_local_path)


def generate_output_report(report_config, **kwargs):
    execution_date = determine_execution_date(**kwargs)

    temp_local_dir = None
    for template_path in report_config['query']['templatePath']:
        temp_hdfs_dir, temp_local_dir, temp_tsv_local_path, temp_csv_local_path, temp_csv_hdfs_path = get_file_paths(
            report_config['name'], report_config['fileName'], execution_date, template_path)
        concat_hdfs_dir_to_local(temp_hdfs_dir, temp_tsv_local_path)
        process_file(temp_tsv_local_path, temp_csv_local_path, TSV_PROCESSOR, report_config['columns'])
        put_local_path_to_hdfs_dir(temp_csv_local_path, temp_hdfs_dir)
        line_count(temp_tsv_local_path)
        line_count(temp_csv_local_path)

    logging.info("Cleaning temp_csv_local_path :({})".format(temp_local_dir))
    remove_file_pattern_using_airflow_user(temp_local_dir)


def process_report_file(report_config, **kwargs):
    execution_date = determine_execution_date(**kwargs)

    temp_local_dir = None
    for template_path in report_config['query']['templatePath']:
        temp_hdfs_dir, temp_local_dir, temp_tsv_local_path, temp_csv_local_path, temp_csv_hdfs_path = get_file_paths(
            report_config['name'], report_config['fileName'], execution_date, template_path)

        get_report(temp_csv_hdfs_path, temp_local_dir)
        process_report(temp_csv_local_path, report_config,
                       DYNAMIC_REPORT_CONFIG[report_config['name']],  execution_date)
        put_local_path_to_hdfs_dir(temp_csv_local_path, temp_hdfs_dir)

    logging.info("Cleaning temp_csv_local_path :({})".format(temp_local_dir))
    remove_file_pattern_using_airflow_user(temp_local_dir)


def upload_to_docstore(report_config, **kwargs):
    execution_date = determine_execution_date(**kwargs)

    temp_local_dir = None
    csv_paths = []
    for template_path in report_config['query']['templatePath']:
        temp_hdfs_dir, temp_local_dir, temp_tsv_local_path, temp_csv_local_path, temp_csv_hdfs_path = get_file_paths(
            report_config['name'], report_config['fileName'], execution_date, template_path)

        get_report(temp_csv_hdfs_path, temp_local_dir)
        csv_paths.append(temp_csv_local_path)

    dynamic_report_config = DYNAMIC_REPORT_CONFIG[report_config['name']]
    logging.info("Generating docstore links for {}".format(csv_paths))
    links = docstore_links(dynamic_report_config['docstore_alias'], csv_paths)
    logging.info("Generated docstore links: {}".format(links))
    kwargs['ti'].xcom_push("docstore_links", links)

    logging.info("Cleaning temp_csv_local_path :({})".format(temp_local_dir))
    remove_file_pattern_using_airflow_user(temp_local_dir)


def email_generated_report(report_config, **kwargs):
    if not report_config.get('email'):
        logging.info("Skipping email. No email config found.")
        return

    email_config = report_config['email']
    execution_date = determine_execution_date(**kwargs)

    logging.info("Generating requestId for sending email via Zencast")
    request_id = str(uuid.uuid1())

    logging.info("Pulling docstore links from XCom")
    docstore_links = ""
    try:
        docstore_links = kwargs['ti'].xcom_pull(task_ids="upload_to_docstore", key="docstore_links")
        logging.info("docstore links: {}".format(docstore_links))
    except Exception:
        logging.error("Pull failed for docstore links")

    context = {
        'EXECUTION_DATE': get_formatted_date(execution_date),
        'DOCSTORE_LINKS': docstore_links
    }
    recipients = email_config['to']
    subject = email_config['subject'].format(**context)
    content = apply_template(read_email_template(email_config['templatePath']).encode(), context)

    logging.info("Initializing Zencast client")
    zencast_client = ZencastClient()

    logging.info(
        "Sending email to({}), subject({}) and requestId({}))".format(recipients, subject, request_id))

    local_file = None
    file_name = None
    temp_local_dir = None
    dynamic_report_config = DYNAMIC_REPORT_CONFIG[report_config['name']]

    for template_path in report_config['query']['templatePath']:
        if 'attachment' in dynamic_report_config and dynamic_report_config['attachment']:
            temp_hdfs_dir, temp_local_dir, temp_tsv_local_path, temp_csv_local_path, temp_csv_hdfs_path = get_file_paths(
                report_config['name'], report_config['fileName'], execution_date, template_path)

            get_report(temp_csv_hdfs_path, temp_local_dir)
            if line_count(temp_csv_local_path) > 1:
                local_file = read_local_file(temp_csv_local_path)
            context['QUERY'] = template_path.split("/")[-1].split(".")[0].upper()
            file_name = report_config['fileName'].format(**context)
        zencast_client.send_email(request_id, recipients, str(content), subject, file_name, local_file)

    logging.info("Cleaning temp_csv_local_path :({})".format(temp_local_dir))
    remove_file_pattern_using_airflow_user(temp_local_dir)


def remove_hdfs_files(report_file_path):
    logging.info("Removing file from path of report {} ".format(report_file_path))
    delete_from_hdfs(report_file_path)


def remove_files(**kwargs):
    remove_hdfs_files(HDFS_CLEANUP_PATH)


def read_local_file(file_path):
    with open(file_path, "rb") as fil:
        return fil.read()


def clean_up_all_tmp_files(report_config, **kwargs):
    execution_date = determine_execution_date(**kwargs)
    template_path = report_config['query']['templatePath'][0]
    temp_hdfs_dir, temp_local_dir, temp_tsv_local_path, temp_csv_local_path, temp_csv_hdfs_path = get_file_paths(
        report_config['name'], report_config['fileName'], execution_date, template_path)

    if temp_hdfs_dir:
        logging.info("Cleaning temp_hdfs_dir :({})".format(temp_hdfs_dir))
        delete_from_hdfs(temp_hdfs_dir)

    logging.info("Cleaning from hdfs location is done for config {}".format(report_config))
