import os
from datetime import timedelta

import pendulum
from airflow import DAG

from mcp_reports.ro.config.config import REPORT_CONFIG_DIR, REPORT_CLEANUP_CONFIG_DIR, DYNAMIC_REPORT_CONFIG
from mcp_reports.ro.operators import execute_hive_query_operator, \
    generate_output_report_operator, email_generated_report_operator, \
    delete_tenant_file_operator, clean_up_all_tmp_files_operator, \
    upload_to_docstore_operator, process_tsv_file_operator
from mcp_reports.ro.utils.common import read_report_config

AIRFLOW_DEFAULT_ARGS = {
    'owner': 'phonepe_mcp_airflow_user',
    'depends_on_past': False,
    'email': ['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': True,
    'retries': 1,
    'retry_delay': timedelta(minutes=10),
    'start_date': pendulum.datetime(2022, 11, 24)
}


def generate_dag(dag_id, config, dynamic_config={}):
    return DAG(
        dag_id,
        default_args=AIRFLOW_DEFAULT_ARGS,
        description=config.get('description', config['name']),
        schedule_interval=dynamic_config['schedule'] if 'schedule' in dynamic_config else config['schedule'],
        catchup=False,
    )


def configure_dag(dag, report_config):
    query_source = report_config['query']['source']
    if query_source == 'hive':
        execute_query_op = execute_hive_query_operator(dag, report_config)
    else:
        raise Exception('Invalid query source supplied')

    generate_report_op = generate_output_report_operator(dag, report_config)
    clean_up_all_tmp_files_op = clean_up_all_tmp_files_operator(dag, report_config)

    execute_query_op >> generate_report_op
    upstream_op = generate_report_op

    dynamic_report_config = DYNAMIC_REPORT_CONFIG[report_config['name']]

    if 'pythonFile' in report_config:
        process_report_op = process_tsv_file_operator(dag, report_config)
        upstream_op >> process_report_op
        upstream_op = process_report_op

    if 'docstore' in dynamic_report_config and dynamic_report_config['docstore']:
        upload_docstore_op = upload_to_docstore_operator(dag, report_config)
        upstream_op >> upload_docstore_op
        upstream_op = upload_docstore_op

    if 'email' in report_config and (
            'disabled' not in report_config['email'] or report_config['email']['disabled'] == False):
        email_report_op = email_generated_report_operator(dag, report_config)
        upstream_op >> email_report_op
        upstream_op = email_report_op

    upstream_op >> clean_up_all_tmp_files_op


def configure_cleanup_dag(dag, report_cleanup_config):
    delete_tenant_file_operator(dag, report_cleanup_config)


def setup_cleanup_tenant(report_cleanup_configs):
    for report_cleanup_config in report_cleanup_configs:
        dag_id = '{}_v{}'.format(report_cleanup_config['name'], report_cleanup_config.get('version', 1))
        dag = generate_dag(dag_id, report_cleanup_config)
        configure_cleanup_dag(dag, report_cleanup_config)
        globals()[dag_id] = dag


def main():
    report_config_files = [os.path.join(dp, f) for dp, dn, filenames in os.walk(REPORT_CONFIG_DIR) for f in filenames]
    report_configs = [read_report_config(rcf) for rcf in report_config_files]

    for report_config in report_configs:
        dag_id = '{}_v{}'.format(report_config['name'], report_config.get('version', 1))
        dynamic_report_config = DYNAMIC_REPORT_CONFIG[report_config['name']]
        dag = generate_dag(dag_id, report_config, dynamic_report_config)
        configure_dag(dag, report_config)
        globals()[dag_id] = dag

    report_cleanup_config_files = [os.path.join(dp, f) for dp, dn, filenames in os.walk(REPORT_CLEANUP_CONFIG_DIR) for f in filenames]
    report_cleanup_configs = [read_report_config(scf) for scf in report_cleanup_config_files]

    setup_cleanup_tenant(report_cleanup_configs)


main()
