#!/usr/bin python

import json
import logging
import requests
from retrying import retry

from mcp_reports.ro.olympus_im_client import OlympusIMClient
from mcp_reports.ro.config.config import ZENCAST_PROTOCOL, ZENCAST_HOST, ZENCAST_PORT, \
    OLYMPUS_PROTOCOL, OLYMPUS_HOST, OLYMPUS_PORT, OLYMPUS_CLIENT_ID, OLYMPUS_CLIENT_KEY

HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

baseUrl = "{}://{}:{}/v1/communication/send/email/multicast"

olympusClient = OlympusIMClient(OLYMPUS_PROTOCOL, OLYMPUS_HOST, OLYMPUS_PORT, OLYMPUS_CLIENT_ID, OLYMPUS_CLIENT_KEY)


class ZencastClient(object):

    @retry(stop_max_attempt_number=3, wait_exponential_multiplier=1000, wait_exponential_max=10000)
    def send_email(self, request_id, receivers, message, subject, file_name, file_content):
        url = baseUrl.format(ZENCAST_PROTOCOL, ZENCAST_HOST, ZENCAST_PORT)

        logging.info("Fetching olympus auth token")
        HEADERS['Authorization'] = olympusClient.get_bearer_auth_token()

        try:
            response = requests.post(url=url,
                                     data=json.dumps(
                                         self.__build_payload_for_send_email(request_id, receivers, message, subject,
                                                                             file_name, file_content.decode("utf-8"))),
                                     headers=HEADERS,
                                     verify=False)
            if response.status_code / 100 == 2:
                logging.info("Success send email zencast response")
                return True
            else:
                logging.info("Zencast send mail, Non success response " + str(response.status_code))
                logging.info(response.content)
        except Exception as e:
            logging.error("Unable to send email (will attempt sending email 3 times): {}".format(e.message))
            raise e

    @retry(stop_max_attempt_number=3, wait_exponential_multiplier=1000, wait_exponential_max=10000)
    def send_email_with_attachment(self, request_id, receivers, message, subject, file_name, file_content):
        url = baseUrl.format(ZENCAST_PROTOCOL, ZENCAST_HOST, ZENCAST_PORT)

        logging.info("Fetching olympus auth token")
        HEADERS['Authorization'] = olympusClient.get_bearer_auth_token()

        try:
            response = requests.post(url=url,
                                     data=json.dumps(
                                         self.__build_payload_for_send_email(request_id, receivers, message, subject,
                                                                             file_name, file_content)),
                                     headers=HEADERS,
                                     verify=False)
            if response.status_code / 100 == 2:
                logging.info("Success send email zencast response")
                return True
            else:
                logging.info("Zencast send mail, Non success response " + str(response.status_code))
                logging.info(response.content)
        except Exception as e:
            logging.error("Unable to send email (will attempt sending email 3 times): {}".format(e.message))
            raise e

    def __build_payload_for_send_email(self, request_id, receivers, message, subject, file_name, file_content):

        payload = {'requestId': request_id, 'receivers': receivers, 'message': message, 'subject': subject,
                   'profileId': "INTERNAL", 'sender': "<EMAIL>", 'senderName': "PhonePe"}

        if file_content:
            payload['attachmentsV2'] = [{
                'type': "DIRECT",
                'fileName': file_name,
                'content': file_content
            }]

        logging.info("Prepared payload for zencast :({})".format(payload))

        return payload
