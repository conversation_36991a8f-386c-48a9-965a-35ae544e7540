import csv
import logging
from dataclasses import dataclass
from enum import Enum
from typing import Dict, Optional, Any

from mcp_reports.dispute_services.utils.common import _validate_file_path, convert_rs_to_crores, \
    get_formatted_date


class CSVHeaders(Enum):
    DATA_FIGURES = 'Data (All figures in Rs)'


class RowIdentifiers(Enum):
    TOTAL_CHARGEBACK = 'Total Chargeback Registered on Stratos'
    AMOUNT_DEBITED = 'Amount debited by NPCI/PGs/Axis Bank'
    MERCHANT_RECOVERY_INITIATED = 'Merchant recovery initiated from Stratos'
    MERCHANT_RECOVERY_PENDING = 'Merchant recovery yet to be initiated'


class DataKeys(Enum):
    TOTAL_CHARGEBACK_REGISTERED = 'total_chargeback_registered'
    AMOUNT_DEBITED = 'amount_debited'
    MERCHANT_RECOVERY_INITIATED = 'merchant_recovery_initiated'
    MERCHANT_RECOVERY_PENDING = 'merchant_recovery_pending'


class ColumnNames(Enum):
    UPI = 'upi'
    PG = 'pg'
    EDC = 'edc'
    TOTAL = 'total'


@dataclass
class ChargebackData:
    upi: str
    pg: str
    edc: str
    total: str

    def to_dict(self) -> Dict[str, str]:
        return {
            ColumnNames.UPI.value: self.upi,
            ColumnNames.PG.value: self.pg,
            ColumnNames.EDC.value: self.edc,
            ColumnNames.TOTAL.value: self.total
        }


def _parse_row_data(row: list) -> ChargebackData:
    return ChargebackData(
        upi=row[1].strip(),
        pg=row[2].strip(),
        edc=row[3].strip(),
        total=row[4].strip()
    )


def _identify_row_type(row_description) -> Optional[DataKeys]:
    row_mapping = {
        RowIdentifiers.TOTAL_CHARGEBACK.value: DataKeys.TOTAL_CHARGEBACK_REGISTERED,
        RowIdentifiers.AMOUNT_DEBITED.value: DataKeys.AMOUNT_DEBITED,
        RowIdentifiers.MERCHANT_RECOVERY_INITIATED.value: DataKeys.MERCHANT_RECOVERY_INITIATED,
        RowIdentifiers.MERCHANT_RECOVERY_PENDING.value: DataKeys.MERCHANT_RECOVERY_PENDING,
    }

    for identifier, data_key in row_mapping.items():
        if identifier in row_description:
            return data_key

    return None


def read_chargeback_financial_impact_data(file_path) -> Dict[str, Dict[str, str]]:
    validated_path = _validate_file_path(file_path)
    data_dict = {}

    try:
        with open(validated_path, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.reader(csvfile)
            row_count = 0

            for row in reader:
                row_count += 1

                if not row or all(cell.strip() == '' for cell in row):
                    continue

                if row[0].strip() == CSVHeaders.DATA_FIGURES.value:
                    continue

                if len(row) == 5:
                    try:
                        row_type = _identify_row_type(row[0])
                        if row_type:
                            chargeback_data = _parse_row_data(row)
                            data_dict[row_type.value] = chargeback_data.to_dict()
                            logging.debug(f"Parsed row {row_count}: {row_type.value}")
                    except Exception as e:
                        logging.error(f"Invalid row {row_count}: {e}")
                        raise e
                else:
                    logging.info(f"Skipping row {row_count} with unknown columns: {len(row)}")

        logging.info(f"Successfully parsed {len(data_dict)} data types from {validated_path}")
        return data_dict

    except FileNotFoundError:
        error_msg = f"CSV file not found: {validated_path}"
        logging.error(error_msg)
        raise FileNotFoundError(error_msg)
    except Exception as e:
        error_msg = f"Unexpected error reading CSV data from {validated_path}: {e}"
        logging.error(error_msg)
        raise ValueError(error_msg)

def _validate_required_data(data_dict) -> None:

    required_keys = [key.value for key in DataKeys]
    missing_keys = [key for key in required_keys if key not in data_dict]

    if missing_keys:
        raise ValueError(
            f"Missing required data types: {missing_keys}. "
            f"Available data types: {list(data_dict.keys())}"
        )


def _build_metric_context(data, metric_prefix) -> Dict[str, str]:
    if not data:
        error_msg = f"No data provided for metric: {metric_prefix}"
        logging.error(error_msg)
        raise ValueError(error_msg)
    return {
        f"{metric_prefix}_UPI": convert_rs_to_crores(data.get(ColumnNames.UPI.value, '0')),
        f"{metric_prefix}_PG": convert_rs_to_crores(data.get(ColumnNames.PG.value, '0')),
        f"{metric_prefix}_EDC": convert_rs_to_crores(data.get(ColumnNames.EDC.value, '0')),
        f"{metric_prefix}_TOTAL": convert_rs_to_crores(data.get(ColumnNames.TOTAL.value, '0'))
    }


def build_context(file_name, start_date, end_date, execution_date) -> Dict[str, Any]:
    try:
        data_dict = read_chargeback_financial_impact_data(file_name)

        _validate_required_data(data_dict)

        total_chargeback = data_dict.get(DataKeys.TOTAL_CHARGEBACK_REGISTERED.value, {})
        amount_debited = data_dict.get(DataKeys.AMOUNT_DEBITED.value, {})
        merchant_recovery_initiated = data_dict.get(DataKeys.MERCHANT_RECOVERY_INITIATED.value, {})
        merchant_recovery_pending = data_dict.get(DataKeys.MERCHANT_RECOVERY_PENDING.value, {})

        context = {
            'START_DATE': get_formatted_date(start_date),
            'END_DATE': get_formatted_date(end_date),
            'EXECUTION_DATE': get_formatted_date(execution_date),
        }

        context.update(_build_metric_context(total_chargeback, 'TOTAL_CHARGEBACK_REGISTERED'))
        context.update(_build_metric_context(amount_debited, 'AMOUNT_DEBITED'))
        context.update(_build_metric_context(merchant_recovery_initiated, 'MERCHANT_RECOVERY_INITIATED'))
        context.update(_build_metric_context(merchant_recovery_pending, 'MERCHANT_RECOVERY_PENDING'))

        logging.info(f"Successfully built chargeback financial impact context with {len(context)} fields")
        logging.info(f"Context data: {context}")

        return context

    except Exception as e:
        error_msg = f"Unexpected error building context from {file_name}: {e}"
        logging.error(error_msg)
        raise e
