import csv
import logging
from mcp_reports.stratos.utils.common import get_formatted_date


def get_formatted_date(d):
    return d.format('YYYY-MM-DD')

def read_last_column(file_path):
    last_column_data = []
    try:
        with open(file_path, 'r', newline='') as csvfile:
            reader = csv.reader(csvfile)
            for row in reader:
                if row:
                    last_column_data.append(row[-1])
    except FileNotFoundError:
        logging.info("Error: File not found at "+str(file_path))
    return last_column_data
def build_context(file_name, start_date, end_date, execution_date):
    last_row = read_last_column(file_name)
    context = {
        'START_DATE': get_formatted_date(start_date),
        'END_DATE': get_formatted_date(end_date),
        'EXECUTION_DATE': get_formatted_date(end_date),
        'TOTAL_DISPUTES_RAISED': last_row[1],
        'AMOUNT_DEBITED_BY_NETWORK': last_row[2],
        'TOTAL_RECOVERY_INITIATED': last_row[3],
        'TOTAL_FINANCIAL_LOSS': last_row[4]
    }
    logging.info("Visibility Context Built :({})".format(context))
    return context

