import logging
import pandas as pd
from mcp_reports.dispute_services.utils.common import get_formatted_date,get_value


def build_context(file_name, start_date, end_date, execution_date):
    df = pd.read_csv(file_name)

    logging.info("toa df :({})".format(df))

    toa_completed_data = df[df['Current State'] == 'TOA_COMPLETED']
    toa_completed_amount = get_value(toa_completed_data, 'Total Amount (In Paise)')
    toa_completed_externally_data = df[df['Current State'] == 'TOA_COMPLETED_EXTERNALLY']
    toa_completed_externally_amount = get_value(toa_completed_externally_data, 'Total Amount (In Paise)')
    toa_pending_data = df[df['Current State'] == 'TOA_PENDING']
    toa_pending_count = get_value(toa_pending_data, 'Total Count')

    toa_failed_after_max_retry_auto_data = df[df['Current State'] == 'TOA_FAILED_AFTER_MAX_AUTO_RETRY']
    toa_failed_after_max_auto_retry_count = get_value(toa_failed_after_max_retry_auto_data, 'Total Count')
    toa_processed = round((toa_completed_amount + toa_completed_externally_amount) / 100, 2)
    action_data = ""

    if toa_pending_count > 0 or toa_failed_after_max_auto_retry_count:
        action_data = "There are some TOAs which are in pending or failed state. You may take the appropriate action from the Stratos Console Dashboard ( https://stratos-console.phonepe.com/ )."
    context = {
        'START_DATE': get_formatted_date(start_date),
        'END_DATE': get_formatted_date(end_date),
        'EXECUTION_DATE': get_formatted_date(execution_date),
        'TOA_PROCESSED': toa_processed,
        'ACTION_DATA': action_data
    }
    logging.info("Context built for TOA :({})".format(context))
    return context
