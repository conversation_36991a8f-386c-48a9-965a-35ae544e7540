import logging
from mcp_reports.dispute_services.utils.common import get_formatted_date

def build_context(file_name, start_date, end_date, execution_date):
    context = {
        'START_DATE': get_formatted_date(start_date),
        'END_DATE': get_formatted_date(end_date),
        'EXECUTION_DATE': get_formatted_date(execution_date)
    }
    logging.info("Default Context Built :({})".format(context))
    return context