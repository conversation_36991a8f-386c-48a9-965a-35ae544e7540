import os
from mcp_reports.dispute_services.config.rosey import ROSEY_CONFIG



PROJECT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
TRANSFORMERS_DIR = os.path.join(PROJECT_DIR, 'transformers')
CONTEXTS_DIR = os.path.join(PROJECT_DIR, 'contexts')
UTIL_DIR = os.path.join(PROJECT_DIR, 'utils')

LOCAL_TEMP_DIR = ROSEY_CONFIG['LOCAL_TEMP_DIR']
HDFS_TEMP_DIR = ROSEY_CONFIG['HDFS_TEMP_DIR']
TSV_FILE_NAME_PATTERN = ROSEY_CONFIG['TSV_FILE_NAME_PATTERN']
REALM = ROSEY_CONFIG['REALM']

OLYMPUS_PROTOCOL = ROSEY_CONFIG['OLYMPUS_PROTOCOL']
OLYMPUS_HOST = ROSEY_CONFIG['OLYMPUS_HOST']
OLYMPUS_PORT = ROSEY_CONFIG['OLYMPUS_PORT']
OLYMPUS_CLIENT_ID = ROSEY_CONFIG['OLYMPUS_CLIENT_ID']
OLYMPUS_CLIENT_KEY = ROSEY_CONFIG['OLYMPUS_CLIENT_KEY']


ZENCAST_PROTOCOL = ROSEY_CONFIG['ZENCAST_PROTOCOL']
ZENCAST_HOST = ROSEY_CONFIG['ZENCAST_HOST']
ZENCAST_PORT = ROSEY_CONFIG['ZENCAST_PORT']

STRATOS_BASE_URL = ROSEY_CONFIG['STRATOS_BASE_URL']

DOCSTORE_CONFIG = ROSEY_CONFIG['DOCSTORE_CONFIG']
DEFAULT_ALIAS_ID = DOCSTORE_CONFIG['DEFAULT_ALIAS_ID']
NAMESPACE = DOCSTORE_CONFIG['DOCSTORE_NAMESPACE']
DOCSTORE_URL = DOCSTORE_CONFIG['DOCSTORE_URL']
DOCSTORE_UI_URL = DOCSTORE_CONFIG['DOCSTORE_UI_URL']
DOCSTORE_BASE_URL = DOCSTORE_CONFIG['DOCSTORE_BASE_URL']
DOCSTORE_TTL = DOCSTORE_CONFIG['DOCSTORE_TTL']

HDFS_USER_ID = ROSEY_CONFIG['HDFS_USER_ID']
HDFS_CMD = ROSEY_CONFIG['HDFS_CMD']
