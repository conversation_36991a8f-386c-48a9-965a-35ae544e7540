import logging
import yaml
import os
from datetime import <PERSON><PERSON><PERSON>

import pendulum
from airflow import DAG
from airflow.utils import dates as dateutils

from configprovider import RoseyService

TEAM_ID = 'mcp'
PROJECT_ID = 'stratosReports'
ROSEY_CONFIG_PATH = '/etc/security/config.yml'

PROJECT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
TRANSFORMERS_DIR = os.path.join(PROJECT_DIR, 'transformers')
CONTEXTS_DIR = os.path.join(PROJECT_DIR, 'contexts')

def get_rosey_config(team_id, project_id):
    logging.info("fetching rosey config for team_id ({}) and project_id ({})".format(team_id, project_id))
    response = RoseyService(ROSEY_CONFIG_PATH).get_project_config(team_id, project_id)
    if response.status_code != 200:
        raise Exception("Failed to fetch rosey config, team ID: {}, project ID: {}".format(team_id, project_id))
    else:
        return yaml.safe_load(response.content)
    
ROSEY_CONFIG = get_rosey_config(TEAM_ID, PROJECT_ID)

