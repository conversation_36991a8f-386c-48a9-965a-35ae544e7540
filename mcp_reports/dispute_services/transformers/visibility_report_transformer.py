import csv
import logging
import tempfile

import pendulum

from mcp_reports.dispute_services.utils.common import remove_file_pattern, create_dir_for_file, append_to_file_top
from mcp_reports.stratos.utils.common import get_formatted_date


def create_csv_in_tmp(filename_prefix):

    try:
        with tempfile.NamedTemporaryFile(mode='w', delete=False, prefix=filename_prefix, suffix='.csv', newline='', encoding='utf-8') as temp_csv:
            writer = csv.writer(temp_csv)
            temp_file_path = temp_csv.name

        logging.info("CSV file created in temporary directory : "+str(temp_file_path))
        return temp_file_path

    except Exception as e:
        logging.info("<PERSON>rror creating CSV file in temporary directory")
        return e


def read_local_file(file_path):
    with open(file_path, "r") as fil:
        return fil.read()

def create_csv(filename):
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
        logging.info("CSV file  created successfully.")

    except Exception as e:
        logging.info("Error creating CSV file: {e}")
        raise e

def csv_to_dict(filename):
    try:
        data_dict = []
        with open(filename, 'r', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['Dispute Amount','Dispute Count','Dispute Type','Message','Dispute Stage']
            reader = csv.DictReader(csvfile,fieldnames)  # Use DictReader
            for row in reader:
                processed_row = {}
                for item in row:
                    try:
                        processed_row[item]=float(row[item]);
                    except ValueError:
                        processed_row[item]=str(row[item]);
                data_dict.append(processed_row)
        return data_dict
    except FileNotFoundError as e:
        logging.error("File not found error")
        raise e
    except Exception as e:
        raise e

def write_row_with_empty_columns(file_path, headers):
    empty_row = [''] * len(headers)
    try:
        with open(file_path, 'a', newline='') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(empty_row)
    except Exception as e:
        logging.error("An error occurred: {e}")
def append_to_csv(filename, dict_row_map,headers):
    try:
        data_row=[];
        for header in headers:
            if header in dict_row_map :
                data_row.append(dict_row_map[header]);
            else:
                data_row.append("");

        with open(filename, 'a', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(data_row)
        logging.info("Data appended to '{filename}' successfully.")
    except FileNotFoundError:
        logging.info("Error: File '{filename}' not found.")
    except Exception as e:
        logging.info("An error occurred: {e}")
        raise e



def get_first_row(data_dictionary,header):
    row={}
    for header_val in header :
        row[header_val]=str(0);
    row[header[0]]="Chargeback Raised against PhonePe"
    total =0;
    for data in data_dictionary:
        if data['Dispute Type']=='UPI_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Total dispute raised' :
            row[header[1]]=data['Dispute Amount']
            total=int(total)+int(data['Dispute Amount'])
        elif data['Dispute Type']=='UPI_CHARGEBACK' and data['Dispute Stage']=='PreArb Level' and data['Message']=='Total dispute raised' :
            row[header[2]]=data['Dispute Amount']
            total=int(total)+int(data['Dispute Amount'])
        elif data['Dispute Type']=='PG_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Total dispute raised' :
            row[header[3]]=data['Dispute Amount']
            total=int(total)+int(data['Dispute Amount'])
        elif data['Dispute Type']=='EDC_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Total dispute raised' :
            row[header[4]]=data['Dispute Amount']
            total=int(total)+int(data['Dispute Amount'])
    row[header[5]]=str(total);
    return row;

def get_second_row(data_dictionary,header):
    row={}
    for header_val in header :
        row[header_val]=str(0);
    row[header[0]]="Amount debited by NPCI/PGs/Axis Bank"
    total =0;
    for data in data_dictionary:
        if data['Dispute Type']=='UPI_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Total dispute debit signal got' :
            row[header[1]]=data['Dispute Amount']
            total=total+int(data['Dispute Amount'])
        elif data['Dispute Type']=='UPI_CHARGEBACK' and data['Dispute Stage']=='PreArb Level' and data['Message']=='Total dispute debit signal got' :
            row[header[2]]=data['Dispute Amount']
            total=total+int(data['Dispute Amount'])
        elif data['Dispute Type']=='PG_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Total dispute debit signal got' :
            row[header[3]]=data['Dispute Amount']
            total=total+int(data['Dispute Amount'])
        elif data['Dispute Type']=='EDC_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Total dispute debit signal got' :
            row[header[4]]=data['Dispute Amount']
            total=total+int(data['Dispute Amount'])
    row[header[5]]=str(total);
    return row;

def get_third_row(data_dictionary,header):
    row={}
    for header_val in header :
        row[header_val]=str(0);
    row[header[0]]="Merchant recovery initiated from Stratos"
    total =0;
    for data in data_dictionary:
        if data['Dispute Type']=='UPI_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Total dispute recovery raised' :
            row[header[1]]=data['Dispute Amount']
            total=total+int(data['Dispute Amount'])
        elif data['Dispute Type']=='UPI_CHARGEBACK' and data['Dispute Stage']=='PreArb Level' and data['Message']=='Total dispute recovery raised' :
            row[header[2]]=data['Dispute Amount']
            total=total+int(data['Dispute Amount'])
        elif data['Dispute Type']=='PG_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Total dispute recovery raised' :
            row[header[3]]=data['Dispute Amount']
            total=total+int(data['Dispute Amount'])
        elif data['Dispute Type']=='EDC_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Total dispute recovery raised' :
            row[header[4]]=data['Dispute Amount']
            total=total+int(data['Dispute Amount'])
    row[header[5]]=str(total);
    return row;






def get_fourth_row(data_dictionary,header):
    row={}
    for header_val in header :
        row[header_val]=str(0);

    row[header[0]]="Financial loss amount (yet to initiate recovery from merchant)"
    total =0;
    for data in data_dictionary:

        if data['Dispute Type']=='UPI_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Total dispute debit signal got' :
            row[header[1]]= int(row[header[1]])+ ( 1 * int(data['Dispute Amount']))
            total=total+int(row[header[1]])+ ( 1 * int(data['Dispute Amount']))
        elif data['Dispute Type']=='UPI_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Total dispute recovery raised' :
            row[header[1]]= int(row[header[1]])+ ( -1 * int(data['Dispute Amount']))
            total=total+int(row[header[1]])+ ( -1 * int(data['Dispute Amount']))

        elif data['Dispute Type']=='UPI_CHARGEBACK' and data['Dispute Stage']=='PreArb Level' and data['Message']=='Total dispute debit signal got' :
            row[header[2]]= int(row[header[2]])+ ( 1 * int(data['Dispute Amount']))
            total=total+int(row[header[2]])+ ( 1 * int(data['Dispute Amount']))
        elif data['Dispute Type']=='UPI_CHARGEBACK' and data['Dispute Stage']=='PreArb Level' and data['Message']=='Total dispute recovery raised' :
            row[header[2]]= int(row[header[2]])+ ( -1 * int(data['Dispute Amount']))
            total=total+int(row[header[2]])+ ( -1 * int(data['Dispute Amount']))

        elif data['Dispute Type']=='PG_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Total dispute debit signal got' :
            row[header[3]]= int(row[header[3]])+ ( 1 * int(data['Dispute Amount']))
            total=total+int(row[header[3]])+ ( 1 * int(data['Dispute Amount']))
        elif data['Dispute Type']=='PG_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Total dispute recovery raised' :
            row[header[3]]= int(row[header[3]])+ ( -1 * int(data['Dispute Amount']))
            total=total+int(row[header[3]])+ ( -1 * int(data['Dispute Amount']))

        elif data['Dispute Type']=='EDC_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Total dispute debit signal got' :
            row[header[4]]= int(row[header[4]]) + ( 1 * int(data['Dispute Amount']))
            total=total+int(row[header[4]]) + ( 1 * int(data['Dispute Amount']))
        elif data['Dispute Type']=='EDC_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Total dispute recovery raised' :
            row[header[4]]= int(row[header[4]])+ ( -1 * int(data['Dispute Amount']))
            total=total+int(row[header[4]])+ ( -1 * int(data['Dispute Amount']))

    row[header[5]]=str(total);
    return row;


def get_fifth_row(data_dictionary,header):
    row={}
    for header_val in header :
        row[header_val]=str(0);
    row[header[0]]="Wrongly Marked Chargeback Rejected on Stratos"
    total =0;
    for data in data_dictionary:
        if data['Dispute Type']=='UPI_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Wrongly Marked Chargeback Rejected on Stratos' :
            row[header[1]]=data['Dispute Amount']
            total=total+int(data['Dispute Amount'])
        elif data['Dispute Type']=='UPI_CHARGEBACK' and data['Dispute Stage']=='PreArb Level' and data['Message']=='Wrongly Marked Chargeback Rejected on Stratos' :
            row[header[2]]=data['Dispute Amount']
            total=total+int(data['Dispute Amount'])
        elif data['Dispute Type']=='PG_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Wrongly Marked Chargeback Rejected on Stratos' :
            row[header[3]]=data['Dispute Amount']
            total=total+int(data['Dispute Amount'])
        elif data['Dispute Type']=='EDC_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Wrongly Marked Chargeback Rejected on Stratos' :
            row[header[4]]=data['Dispute Amount']
            total=total+int(data['Dispute Amount'])
    row[header[5]]=str(total);
    return row;

def get_sixth_row(data_dictionary,header):
    row={}
    for header_val in header :
        row[header_val]=str(0);
    row[header[0]]="Recovery event not raised from Stratos even after chargeback is accepted on stratos"
    total =0;
    for data in data_dictionary:
        if data['Dispute Type']=='UPI_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Recovery event not raised from Stratos even after chargeback is accepted on stratos' :
            row[header[1]]=data['Dispute Amount']
            total=total+int(data['Dispute Amount'])
        elif data['Dispute Type']=='UPI_CHARGEBACK' and data['Dispute Stage']=='PreArb Level' and data['Message']=='Recovery event not raised from Stratos even after chargeback is accepted on stratos' :
            row[header[2]]=data['Dispute Amount']
            total=total+int(data['Dispute Amount'])
        elif data['Dispute Type']=='PG_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Recovery event not raised from Stratos even after chargeback is accepted on stratos' :
            row[header[3]]=data['Dispute Amount']
            total=total+int(data['Dispute Amount'])
        elif data['Dispute Type']=='EDC_CHARGEBACK' and data['Dispute Stage']=='First Level' and data['Message']=='Recovery event not raised from Stratos even after chargeback is accepted on stratos' :
            row[header[4]]=data['Dispute Amount']
            total=total+int(data['Dispute Amount'])
    row[header[5]]=str(total);
    return row;

def process_output(current_csv_file):
    header = ["Data (All figures in Paisa)", "UPI First Level", "UPI Pre Arb", "PG","EDC", "Total"]
    temp_csv_path = create_csv_in_tmp("updated_report")
    logging.info(">>> path :  "+str(temp_csv_path))
    data_dictionary = csv_to_dict(current_csv_file)
    if data_dictionary:
        logging.info(data_dictionary)
    first_row = get_first_row(data_dictionary,header);
    append_to_csv(temp_csv_path,first_row,header)
    second_row = get_second_row(data_dictionary,header);
    append_to_csv(temp_csv_path,second_row,header)

    third_row = get_third_row(data_dictionary,header);
    append_to_csv(temp_csv_path,third_row,header)

    fourth_row = get_fourth_row(data_dictionary,header);
    append_to_csv(temp_csv_path,fourth_row,header)

    write_row_with_empty_columns(temp_csv_path,header)

    fifth_row = get_fifth_row(data_dictionary,header);
    append_to_csv(temp_csv_path,fifth_row,header)

    sixth_row = get_sixth_row(data_dictionary,header);
    append_to_csv(temp_csv_path,sixth_row,header)
    return temp_csv_path;

def process(tsv_file_path, csv_file_path):

    with open(tsv_file_path, 'r') as fin:
        cr = csv.reader(fin, delimiter='\t')
        file_contents = [line for line in cr]

    with open(csv_file_path, 'a') as csvfile:
        writer = csv.writer(csvfile, delimiter=',', quotechar='|', quoting=csv.QUOTE_MINIMAL)
        writer.writerows(file_contents)
    tmp_path_file = process_output(csv_file_path);
    remove_file_pattern(csv_file_path)
    create_dir_for_file(csv_file_path)
    create_csv(csv_file_path)
    append_to_file_top(csv_file_path, read_local_file(tmp_path_file))


def get_formatted_date(d):
    return d.format('YYYY-MM-DD')

def read_last_column(file_path):
    last_column_data = []
    try:
        with open(file_path, 'r', newline='') as csvfile:
            reader = csv.reader(csvfile)
            for row in reader:
                if row:
                    last_column_data.append(row[-1])
    except FileNotFoundError:
        logging.info("Error: File not found at "+str(file_path))
    return last_column_data
def build_context(file_name, start_date, end_date, execution_date):
    last_row = read_last_column(file_name)
    context = {
        'START_DATE': get_formatted_date(end_date),
        'END_DATE': get_formatted_date(end_date),
        'EXECUTION_DATE': get_formatted_date(end_date),
        'TOTAL_DISPUTES_RAISED': last_row[1],
        'AMOUNT_DEBITED_BY_NETWORK': last_row[2],
        'TOTAL_RECOVERY_INITIATED': last_row[3],
        'TOTAL_FINANCIAL_LOSS': last_row[4]
    }
    logging.info("Default Context Built :({})".format(context))
    return context

if __name__ == '__main__':
   context =  build_context('/Users/<USER>/Office/newCode/mcp/lucy-mcp-airflow-dag/mcp_reports/dispute_services/utils/STRATOS_VISIBILITY_REPORT.csv'
                            ,pendulum.now(),pendulum.now(),pendulum.now())
   print(context)
   end_date =pendulum.now()
   start_date = end_date.subtract(days=3 - 1).start_of('day')
   print(get_formatted_date(start_date))
   print(start_date)

