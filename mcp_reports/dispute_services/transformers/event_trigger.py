import requests
import urllib3
import logging
import csv
from mcp_reports.dispute_services.utils.call_foxtrot import call_foxtrot
from mcp_reports.dispute_services.utils.zencast_client import olympusClient
urllib3.disable_warnings()


def get_token():
    token = olympusClient.get_bearer_auth_token()
    return token
   
def debit_signal(dag_config):
    opcode = dag_config['tasks_config']['EXECUTE_FOXTROT'].get('opcode','query')
    filter = dag_config['tasks_config']['EXECUTE_FOXTROT'].get('filter')
    limit = dag_config['tasks_config']['EXECUTE_FOXTROT'].get('limit',1000)
    logging.info(filter)
    filters = []
    for i in filter:
        values = i.split(',')
        filter_operator = values[1]
        filter_field = values[0]
        filter_value = values[2]
        filters.append({
                "operator" : filter_operator,
                "field" : filter_field,
                "cachedResultsAccepted" : False,
                "value" : filter_value
            })
    DATA = {
            "opcode" : opcode,
            "filters" : filters,
            "bypassCache" : False,
            "userDetails" : None,
            "serviceUserPrincipal" : None,
            "requestTags" : { },
            "sourceType" : "ECHO_BROWSE_EVENTS",
            "extrapolationFlag" : False,
            "table" : "stratos",
            "limit" : limit
            }
    state_list = dag_config['tasks_config']['API_REPORT'].get('state_check')
    record = call_foxtrot(DATA, state_list,dag_config)
    return record

def process(dag_config, csv_file_path):
    with open(csv_file_path, 'w') as csvfile:
        writer = csv.writer(csvfile, delimiter=',', quotechar='|', quoting=csv.QUOTE_MINIMAL)
        row = []
        writer.writerow(row)
        
    logging.info('starting python script event_trigger_upi')
    msg_body = debit_signal(dag_config)
    logging.info(msg_body)
    return msg_body

    
    
