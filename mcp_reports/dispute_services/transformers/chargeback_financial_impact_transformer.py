import csv
import logging
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List
from mcp_reports.dispute_services.utils.common import remove_file_pattern, create_dir_for_file, append_to_file_top, \
    append_row_to_csv, write_empty_row, convert_tsv_to_csv, create_empty_csv, read_file_content, \
    create_temporary_csv, _parse_float_value, append_to_file_top_safely, convert_paise_to_rs


class MetricKeys(Enum):
    DISPUTED_AMOUNT = 'disputed_amount'
    AMOUNT_DEBITED = 'amount_debited'
    AMOUNT_RECOVERED = 'amount_recovered'
    LOSS = 'loss'
    WRONGLY_MARKED_CHARGEBACK_AMOUNT = 'wrongly_marked_chargeback_amount'
    RGCS_ACCEPTED_AND_NPCI_ACCEPTED = 'rgcs_accepted_and_npci_accepted'
    DEBIT_SIGNAL_PROCESSED_PENDING_ON_OPS = 'debit_signal_processed_pending_on_ops'
    PENDING_ON_OPS_TO_MOVE = 'pending_on_ops_to_move'
    ABSORBED_OR_REQUESTED_BY_OPS = 'absorbed_or_requested_by_ops'
    DEBIT_SIGNAL_NOT_PROCESSED = 'debit_signal_not_processed'


class ChannelKeys(Enum):
    UPI_CHARGEBACK = 'upi_chargeback'
    PG_CHARGEBACK = 'pg_chargeback'
    EDC_CHARGEBACK = 'edc_chargeback'
    TOTAL = 'total'


@dataclass
class ChargebackMetrics:
    upi_chargeback: float = 0
    pg_chargeback: float = 0
    edc_chargeback: float = 0
    total: float = 0

    def to_string_dict(self) -> Dict[str, str]:
        return {
            ChannelKeys.UPI_CHARGEBACK.value: str(self.upi_chargeback),
            ChannelKeys.PG_CHARGEBACK.value: str(self.pg_chargeback),
            ChannelKeys.EDC_CHARGEBACK.value: str(self.edc_chargeback),
            ChannelKeys.TOTAL.value: str(self.total)
        }


@dataclass
class ReportHeaders:
    MAIN_REPORT = ["Data (All figures in Rs)", "UPI", "PG", "EDC", "Total"]
    BREAKDOWN_REPORT = [
        "Reasons for 'Merchant recovery yet to be initiated'",
        "UPI", "PG", "EDC", "Total",
        "Description", "Owner", "Expected Actions"
    ]


def parse_csv_to_metrics_dict(filename) -> Dict[str, ChargebackMetrics]:
    try:
        data_dict = {}
        with open(filename, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.reader(csvfile)
            header_skipped = False

            for row_num, row in enumerate(reader, 1):
                if len(row) < 5:
                    logging.info(f"Skipping row {row_num} with insufficient columns: {len(row)}")
                    continue

                if not header_skipped and row[0].strip() == 'metric':
                    header_skipped = True
                    continue

                metric_name = row[0].strip()
                if metric_name:
                    data_dict[metric_name] = ChargebackMetrics(
                        upi_chargeback=convert_paise_to_rs(_parse_float_value(row[1])),
                        pg_chargeback=convert_paise_to_rs(_parse_float_value(row[2])),
                        edc_chargeback=convert_paise_to_rs(_parse_float_value(row[3])),
                        total=convert_paise_to_rs(_parse_float_value(row[4]))
                    )
                    logging.info(f"metric {metric_name} data: {data_dict[metric_name]}")

        logging.info(f"Parsed {len(data_dict)} metrics from {filename}")
        return data_dict

    except FileNotFoundError:
        error_msg = f"CSV file not found: {filename}"
        logging.error(error_msg)
        raise FileNotFoundError(error_msg)
    except Exception as e:
        error_msg = f"Failed to parse CSV file {filename}: {e}"
        logging.error(error_msg)
        raise RuntimeError(error_msg)


class MainReportRowGenerator:

    @staticmethod
    def _create_empty_row(headers) -> Dict[str, str]:
        return {header: "" for header in headers}

    @staticmethod
    def _populate_metrics_data(row, headers, metrics) -> None:
        if metrics and len(headers) >= 5:
            metrics_dict = metrics.to_string_dict()
            row[headers[1]] = metrics_dict[ChannelKeys.UPI_CHARGEBACK.value]
            row[headers[2]] = metrics_dict[ChannelKeys.PG_CHARGEBACK.value]
            row[headers[3]] = metrics_dict[ChannelKeys.EDC_CHARGEBACK.value]
            row[headers[4]] = metrics_dict[ChannelKeys.TOTAL.value]

    @classmethod
    def get_total_chargeback_registered_row(
        cls, data_dictionary, headers) -> Dict[str, str]:
        row = cls._create_empty_row(headers)
        row[headers[0]] = "Total Chargeback Registered on Stratos"

        metrics = data_dictionary.get(MetricKeys.DISPUTED_AMOUNT.value)
        cls._populate_metrics_data(row, headers, metrics)
        return row

    @classmethod
    def get_amount_debited_row(cls, data_dictionary, headers) -> Dict[str, str]:
        row = cls._create_empty_row(headers)
        row[headers[0]] = "Amount debited by NPCI/PGs/Axis Bank\n(i.e. Chargeback Accepted by PhonePe)"

        metrics = data_dictionary.get(MetricKeys.AMOUNT_DEBITED.value)
        cls._populate_metrics_data(row, headers, metrics)
        return row

    @classmethod
    def get_merchant_recovery_initiated_row(cls, data_dictionary, headers) -> Dict[str, str]:
        row = cls._create_empty_row(headers)
        row[headers[0]] = "Merchant recovery initiated from Stratos for Accepted Chargebacks"

        metrics = data_dictionary.get(MetricKeys.AMOUNT_RECOVERED.value)
        cls._populate_metrics_data(row, headers, metrics)
        return row

    @classmethod
    def get_merchant_recovery_pending_row(cls, data_dictionary, headers) -> Dict[str, str]:
        row = cls._create_empty_row(headers)
        row[headers[0]] = "Merchant recovery yet to be initiated for Accepted Chargebacks*"

        metrics = data_dictionary.get(MetricKeys.LOSS.value)
        cls._populate_metrics_data(row, headers, metrics)
        return row


@dataclass
class LossBreakdownRowConfig:
    title: str
    description: str
    owner: str
    expected_actions: str
    metric_key: str


class LossBreakdownReportRowGenerator:
    LOSS_BREAKDOWN_CONFIGS = [
        LossBreakdownRowConfig(
            title="Incorrect Representment Marking",
            description="Chargebacks were accepted by NPCI/PG/Bank but were wrongly marked as \"Representment Completed\" on Stratos. This led to no recovery being initiated.",
            owner="Chargeback Ops",
            expected_actions="Chargeback Ops Team to review and correct the representment status on Stratos.",
            metric_key=MetricKeys.WRONGLY_MARKED_CHARGEBACK_AMOUNT.value
        ),
        LossBreakdownRowConfig(
            title="Discrepancy Between RGCS & NPCI Status",
            description="RGCS shows chargeback acceptance completed, but NPCI still accepted the chargeback and debited funds. This discrepancy led to unexpected loss.",
            owner="Chargeback Ops",
            expected_actions="Ops Team to investigate and provide clarification for such inconsistencies.",
            metric_key=MetricKeys.RGCS_ACCEPTED_AND_NPCI_ACCEPTED.value
        ),
        LossBreakdownRowConfig(
            title="Recovery Pending Post Debit Signal",
            description="Debit signal has been successfully processed, but recovery flow has not been triggered by Ops on Stratos.",
            owner="Chargeback Ops",
            expected_actions="Ops Team to initiate recovery for these accepted chargebacks.",
            metric_key=MetricKeys.DEBIT_SIGNAL_PROCESSED_PENDING_ON_OPS.value
        ),
        LossBreakdownRowConfig(
            title="Chargeback Stuck in Intermediate State",
            description="Chargebacks are still in an investigation or intermediate state and have not moved to the correct recovery path.",
            owner="Chargeback Ops",
            expected_actions="Ops Team to move these chargebacks to the appropriate state for closure/recovery.",
            metric_key=MetricKeys.PENDING_ON_OPS_TO_MOVE.value
        ),
        LossBreakdownRowConfig(
            title="Finance Absorption Pending",
            description="Ops team has requested finance to absorb the chargeback loss for certain cases. These are currently awaiting Finance team's decision.",
            owner="Finance",
            expected_actions="To evaluate and confirm the cases where chargeback loss is to be absorbed.",
            metric_key=MetricKeys.ABSORBED_OR_REQUESTED_BY_OPS.value
        ),
        LossBreakdownRowConfig(
            title="Debit Signal Not Triggered on Stratos",
            description="Chargeback acceptance was correctly marked on Stratos, but debit signal was not processed due to system-level or process miss.",
            owner="Stratos",
            expected_actions="To ensure debit signal is processed for these cases.",
            metric_key=MetricKeys.DEBIT_SIGNAL_NOT_PROCESSED.value
        )
    ]

    @staticmethod
    def _create_empty_breakdown_row(headers) -> Dict[str, str]:
        return {header: "" for header in headers}

    @staticmethod
    def _populate_breakdown_metrics(row, headers, metrics) -> None:
        if metrics and len(headers) >= 5:
            metrics_dict = metrics.to_string_dict()
            row[headers[1]] = metrics_dict[ChannelKeys.UPI_CHARGEBACK.value]
            row[headers[2]] = metrics_dict[ChannelKeys.PG_CHARGEBACK.value]
            row[headers[3]] = metrics_dict[ChannelKeys.EDC_CHARGEBACK.value]
            row[headers[4]] = metrics_dict[ChannelKeys.TOTAL.value]

    @classmethod
    def generate_breakdown_row(cls, config, data_dictionary, headers) -> Dict[str, str]:
        row = cls._create_empty_breakdown_row(headers)

        if len(headers) >= 8:
            row[headers[0]] = config.title
            row[headers[5]] = config.description
            row[headers[6]] = config.owner
            row[headers[7]] = config.expected_actions

            metrics = data_dictionary.get(config.metric_key)
            cls._populate_breakdown_metrics(row, headers, metrics)

        return row

    @classmethod
    def generate_all_breakdown_rows(cls, data_dictionary, headers) -> List[Dict[str, str]]:
        return [
            cls.generate_breakdown_row(config, data_dictionary, headers)
            for config in cls.LOSS_BREAKDOWN_CONFIGS
        ]


def generate_chargeback_report(input_csv_file) -> str:
    try:
        headers = ReportHeaders()
        main_headers = headers.MAIN_REPORT
        breakdown_headers = headers.BREAKDOWN_REPORT

        temp_csv_path = create_temporary_csv("chargeback_analysis_report")
        logging.info(f"Generating report at: {temp_csv_path}")

        data_dictionary = parse_csv_to_metrics_dict(input_csv_file)

        main_generator = MainReportRowGenerator()
        breakdown_generator = LossBreakdownReportRowGenerator()

        main_rows = [
            main_generator.get_total_chargeback_registered_row(data_dictionary, main_headers),
            main_generator.get_amount_debited_row(data_dictionary, main_headers),
            main_generator.get_merchant_recovery_initiated_row(data_dictionary, main_headers),
            main_generator.get_merchant_recovery_pending_row(data_dictionary, main_headers)
        ]

        for row in main_rows:
            append_row_to_csv(temp_csv_path, row, main_headers)

        write_empty_row(temp_csv_path, len(main_headers))
        write_empty_row(temp_csv_path, len(main_headers))

        breakdown_header_row = {header: header for header in breakdown_headers}
        append_row_to_csv(temp_csv_path, breakdown_header_row, breakdown_headers)

        breakdown_rows = breakdown_generator.generate_all_breakdown_rows(data_dictionary, breakdown_headers)
        for row in breakdown_rows:
            append_row_to_csv(temp_csv_path, row, breakdown_headers)

        logging.info(f"Report generation completed: {temp_csv_path}")
        return temp_csv_path

    except Exception as e:
        error_msg = f"Failed to generate chargeback report from {input_csv_file}: {e}"
        logging.error(error_msg)
        raise RuntimeError(error_msg)


def process(tsv_file_path, csv_file_path) -> None:
    try:
        logging.info(f"Starting chargeback transformation: {tsv_file_path} -> {csv_file_path}")

        convert_tsv_to_csv(tsv_file_path, csv_file_path)

        temp_report_path = generate_chargeback_report(csv_file_path)

        remove_file_pattern(csv_file_path)
        create_dir_for_file(csv_file_path)
        create_empty_csv(csv_file_path)

        report_content = read_file_content(temp_report_path)
        append_to_file_top_safely(csv_file_path, report_content)

        logging.info(f"Chargeback transformation completed successfully: {csv_file_path}")

    except Exception as e:
        error_msg = f"Failed to process chargeback transformation: {e}"
        logging.error(error_msg)
        raise ValueError(error_msg)
