import csv
import logging

from mcp_reports.dispute_services.utils.stratos_client import StratosClient


def process(tsv_file_path, csv_file_path):

    global response_map

    stratos_client = StratosClient()
    try:
        res = stratos_client.get_enums("")
        response_map = res.json()
    except Exception as e:
        logging.error("Failed to get enum values: {}".format(e))
        raise e

    logging.info("Successfully get the enum values: {}".format(response_map))

    with open(csv_file_path, 'w') as csvfile:
        writer = csv.writer(csvfile, delimiter=',', quotechar='|', quoting=csv.QUOTE_MINIMAL)
        tsv_file = open(tsv_file_path)
        rows = list(csv.reader(tsv_file, delimiter='\t'))
        logging.info("Number of rows found for Delhiveryonline in last 1 month are {}".format(len(rows)))

        for row in rows:
            logging.info(row)
            row[2]=response_map.get('DISPUTE_STAGE').get(row[2])
            row[5]=response_map.get('DISPUTE_WORK_FLOW_STATE').get(row[5])

            writer.writerow(row)


