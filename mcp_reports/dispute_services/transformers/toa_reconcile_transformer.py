import csv
import logging
from mcp_reports.dispute_services.utils.stratos_client import StratosClient


def process(tsv_file_path, csv_file_path):
    stratos_client = StratosClient()

    with open(csv_file_path, 'w') as csvfile:
        writer = csv.writer(csvfile, delimiter=',', quotechar='|', quoting=csv.QUOTE_MINIMAL)
        tsv_file = open(tsv_file_path)
        rows = list(csv.reader(tsv_file, delimiter='\t'))
        logging.info("Number of rows found are {}".format(len(rows)))

        for row in rows:
            try:
                dispute_type_enums = stratos_client.get_enums("?enumClasses=DISPUTE_TYPE").json()
                logging.info("Dispute type is {}".format(dispute_type_enums.get(row[2])))
                res = stratos_client.toa_reconcile(row[0], row[1], dispute_type_enums.get(row[2]))
                if res.status_code != 200:
                    writer.writerow(row)
            except Exception as e:
                logging.error("Failed to reconcile: {}".format(e))
