import logging
import traceback
import uuid
from airflow.operators.python_operator import PythonOperator
from hive.hook.secured_hive_cli_hook import Secure<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ook

from mcp_reports.dispute_services.config.config import HDFS_TEMP_DIR, REALM, HDFS_USER_ID
from mcp_reports.dispute_services.utils.common import determine_execution_date, get_date_range, \
    get_file_paths, create_hdfs_dir, format_query, remove_file_pattern, \
    remove_file_pattern_using_airflow_user, concat_hdfs_to_local, convert_tsv_to_report_format, \
    delete_from_hdfs, append_to_file_top, concat_local_to_hdfs, line_count, get_context, \
    get_formatted_date, read_local_file, get_api_file_paths, write_units_to_api_file, get_tenant_query_template
from mcp_reports.dispute_services.utils.docstore import docstore_links
from mcp_reports.dispute_services.utils.zencast_client import ZencastClient


def execute_hive_query_op(dag, dag_config):
    return PythonOperator(
        task_id='execute_hive_query',
        provide_context=True,
        python_callable=execute_hive_query,
        op_kwargs={
            'dag_config': dag_config
        },
        dag=dag,
    )

def execute_hive_query(dag_config, **kwargs):
    logging.info(dag_config)
    execution_date = determine_execution_date(**kwargs)
    start_date, end_date = get_date_range(execution_date, dag_config)
    query = get_tenant_query_template(dag_config['tasks_config']['EXECUTE_HIVE']['query'])
    logging.info(query)

    hdfs_tsv_file_path, _, _, _ = get_file_paths(dag_config['dag_name'],
                                                    dag_config['filename'],
                                                    start_date, end_date, execution_date,
                                                    dag_config['tasks_config']['EXECUTE_HIVE']['task_id'])

    logging.info(hdfs_tsv_file_path)
    create_hdfs_dir(HDFS_TEMP_DIR)

    hql = format_query(query, start_date, end_date, execution_date)
    hql = """
    set tez.queue.name = default; 
    set hive.exec.compress.output=false; 
    set hive.merge.tezfiles=true;
    INSERT OVERWRITE DIRECTORY '{output_file}' ROW FORMAT DELIMITED FIELDS TERMINATED BY 
    '\t' STORED AS TEXTFILE
    """.format(output_file=hdfs_tsv_file_path) + hql

    logging.info("Hql Query: ({})".format(hql))

    hive_hook = SecuredHiveCliHook(user=HDFS_USER_ID, realm=REALM)
    hive_hook.run_cli(hql)

def generate_report_op(dag, dag_config):
    return PythonOperator(
        task_id='generate_output_report',
        provide_context=True,
        python_callable=generate_output_report,
        op_kwargs={
            'dag_config': dag_config,
        },
        dag=dag,
    )

def generate_output_report(dag_config, **kwargs):
    execution_date = determine_execution_date(**kwargs)
    logging.info('kwargs is: ({})'.format(kwargs))
    start_date, end_date = get_date_range(execution_date, dag_config)

    columns = dag_config['tasks_config']['GENERATE_REPORT']['column']
    logging.info(columns)
    if dag_config.get('fileType', '.csv') == '.psv':
        columns = "|".join(dag_config['tasks_config']['GENERATE_REPORT']['column'])

    local_report_file_path = ''
    hdfs_report_file_path = ''
    clean_up_csv_if_present = True

    hdfs_tsv_file_path, hdfs_report_file_path, local_tsv_file_path, local_report_file_path, \
            = get_file_paths(dag_config['dag_name'],
                             dag_config['filename'],
                             start_date, end_date, execution_date,
                             dag_config['tasks_config']['EXECUTE_HIVE']['task_id'])

    if clean_up_csv_if_present:
        remove_file_pattern(local_report_file_path)
        remove_file_pattern_using_airflow_user(local_tsv_file_path)
        clean_up_csv_if_present = False

    concat_hdfs_to_local(hdfs_tsv_file_path, local_tsv_file_path)
    rows = convert_tsv_to_report_format(local_tsv_file_path, local_report_file_path,
                                    dag_config['tasks_config']['GENERATE_REPORT'].get('transformer', 'default.py'))
    kwargs['ti'].xcom_push("process", rows)
    delete_from_hdfs(hdfs_tsv_file_path)
    remove_file_pattern_using_airflow_user(local_tsv_file_path)

    append_to_file_top(local_report_file_path, columns)
    concat_local_to_hdfs(local_report_file_path, hdfs_report_file_path)
    remove_file_pattern(local_report_file_path)


def api_report_op(dag,dag_config):
    return PythonOperator(
        task_id='generate_api_report',
        provide_context=True,
        python_callable=generate_api_report,
        op_kwargs={
            'dag_config': dag_config,
        },
        dag=dag,
    )

def generate_api_report(dag_config, **kwargs):
    execution_date = determine_execution_date(**kwargs)
    logging.info('kwargs is: ({})'.format(kwargs))
    start_date, end_date = get_date_range(execution_date, dag_config)

    local_report_file_path = ''
    hdfs_report_file_path = ''
    clean_up_csv_if_present = True

    hdfs_report_file_path, local_report_file_path, \
            = get_api_file_paths(dag_config['dag_name'],
                             dag_config['filename'],
                             start_date, end_date, execution_date)

    if clean_up_csv_if_present:
        remove_file_pattern(local_report_file_path)
        clean_up_csv_if_present = False

    rows = write_units_to_api_file(dag_config, local_report_file_path,
                                   dag_config['tasks_config']['API_REPORT'].get('transformer', 'default.py'))
    kwargs['ti'].xcom_push("process", rows)

    concat_local_to_hdfs(local_report_file_path, hdfs_report_file_path)
    remove_file_pattern(local_report_file_path)


def upload_docstore_op(dag, dag_config):
    return PythonOperator(
        task_id='upload_to_docstore',
        provide_context=True,
        python_callable=upload_to_docstore,
        op_kwargs={
            'dag_config': dag_config,
        },
        dag=dag,
    )

def upload_to_docstore(dag_config, **kwargs):
    execution_date = determine_execution_date(**kwargs)
    start_date, end_date = get_date_range(execution_date, dag_config)

    csv_paths = []

    _, hdfs_report_file_path, _, local_report_file_path, \
        = get_file_paths(dag_config['dag_name'],
                             dag_config['filename'],
                             start_date, end_date, execution_date,
                             dag_config['tasks_config']['EXECUTE_HIVE']['task_id'])

    concat_hdfs_to_local(hdfs_report_file_path, local_report_file_path)
    csv_paths.append(local_report_file_path)

    alias_id = dag_config['tasks_config']['UPLOAD_TO_DOCSTORE']['alias_id']
    logging.info("Generating docstore links for {}".format(csv_paths))
    links = docstore_links(alias_id, csv_paths)
    logging.info("Generated docstore links: {}".format(links))
    kwargs['ti'].xcom_push("docstore_links", links)

    logging.info("Cleaning temp_csv_local_path :({})".format(local_report_file_path))
    remove_file_pattern_using_airflow_user(local_report_file_path)


def email_report_op(dag, dag_config):
    return PythonOperator(
        task_id='email_generated_report',
        provide_context=True,
        python_callable=email_generated_report,
        op_kwargs={
            'dag_config': dag_config,
        },
        dag=dag,
    )



def email_generated_report(dag_config, **kwargs):
    if not dag_config['tasks_config']['EMAIL_REPORT'].get('email'):
        logging.info("Skipping email. No email config found.")
        return

    recipients = dag_config['tasks_config']['EMAIL_REPORT'].get('email')
    execution_date = determine_execution_date(**kwargs)
    start_date, end_date = get_date_range(execution_date, dag_config)

    run_type = dag_config.get('execution_type','hive')
    if run_type == 'hive':
        _, hdfs_report_file_path, _, local_report_file_path = get_file_paths(dag_config['dag_name'],
                                dag_config['filename'],
                                start_date, end_date, execution_date,
                                dag_config['tasks_config']['EXECUTE_HIVE']['task_id'])
    elif run_type == 'api':
        hdfs_report_file_path, local_report_file_path = get_api_file_paths(dag_config['dag_name'],
                                dag_config['filename'],
                                start_date, end_date, execution_date)

    concat_hdfs_to_local(hdfs_report_file_path, local_report_file_path)

    if line_count(local_report_file_path) == 1 and run_type == 'hive':
        logging.info("Skipping email. No records present.")
        remove_file_pattern_using_airflow_user(local_report_file_path)
        delete_from_hdfs(hdfs_report_file_path)
        return

    context = get_context(dag_config['tasks_config']['EMAIL_REPORT'].get('context', 'default.py'), local_report_file_path, start_date, end_date,
                          execution_date)

    try:
        if run_type == 'hive':
            rows = kwargs['ti'].xcom_pull(task_ids="generate_output_report", key="process")
            logging.info("checking for rows data: {}".format(rows[0]))
            logging.info("rows: {}".format(rows))
        elif run_type == 'api':
            rows = kwargs['ti'].xcom_pull(task_ids="generate_api_report", key="process")
            logging.info("checking for rows data: {}".format(rows[0]))
            logging.info("rows: {}".format(rows))
    except Exception:
        logging.error("Pull failed for rows data")
        rows = ''

    context.update({
        'ROWS': rows
    })

    if dag_config['tasks_config']['UPLOAD_TO_DOCSTORE'].get('docstore'):
        logging.info(f"Pulling docstore links for {dag_config['dag_name']} from XCom")
        docstore_links = ""
        try:
            docstore_links = kwargs['ti'].xcom_pull(task_ids="upload_to_docstore", key="docstore_links")
            logging.info("checking for empty docstore link: {}".format(docstore_links[0]))
            logging.info("docstore links: {}".format(docstore_links))
        except Exception:
            logging.error("Pull failed for docstore links")


        context.update({
            'EXECUTION_DATE': get_formatted_date(execution_date),
            'DOCSTORE_LINKS': docstore_links
        })
    subject = dag_config['tasks_config']['EMAIL_REPORT']['subject'].format(**context)

    # content = apply_template(dag_config['tasks_config']['EMAIL_REPORT']['template'].encode(), context)
    content = dag_config['tasks_config']['EMAIL_REPORT']['template'].format(**context)

    logging.info(context)
    logging.info(content)

    logging.info("Generating requestId for sending email via Zencast")
    request_id = str(uuid.uuid1())

    logging.info("Initializing Zencast client")
    zencast_client = ZencastClient()
    file_name = dag_config['filename'].format(**context)
    try:
        logging.info("Sending email with content %s", content)
        if dag_config['tasks_config']['UPLOAD_TO_DOCSTORE'].get('docstore'):
            zencast_client.send_email(request_id, recipients, str(content), subject, file_name, None)
        else:
            zencast_client.send_email(request_id, recipients, str(content), subject, file_name, read_local_file(local_report_file_path))
    except Exception:
        logging.error("Failed to send E-Mail. Exception: %s" % traceback.format_exc())

    remove_file_pattern_using_airflow_user(local_report_file_path)
    delete_from_hdfs(hdfs_report_file_path)
