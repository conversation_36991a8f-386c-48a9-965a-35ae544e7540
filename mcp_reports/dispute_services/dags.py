import logging
from datetime import timed<PERSON><PERSON>

import pendulum
from airflow import DAG

from mcp_reports.dispute_services.config.config import ROSEY_CONFIG
from mcp_reports.dispute_services.stratos_dags import execute_hive_query_op, \
    generate_report_op, email_report_op, upload_docstore_op, api_report_op

AIRFLOW_DEFAULT_ARGS = {
    'owner': 'phonepe_mcp_airflow_user',
    'depends_on_past': False,
    'email': ['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': True,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
    'start_date': pendulum.datetime(2021, 10, 1)
}


def generate_dag_id(dag_config):
    dag_name = dag_config['dag_name']
    dag_version = dag_config.get('dag_version', 1)
    return "{}_v{}".format(dag_name, dag_version)

def generate_dag(dag_id, dag_config):
     return DAG(
        dag_id,
        default_args=AIRFLOW_DEFAULT_ARGS,
        description=dag_config.get('description', dag_config['dag_name']),
        schedule_interval=dag_config['schedule_interval'],
        catchup=False,
    )
     
def build_execute_hive_task(dag_config, dag):
    return execute_hive_query_op(dag, dag_config)

def build_output_report_task(dag_config, dag):
     return generate_report_op(dag, dag_config)

def build_generate_api_report_task(dag_config,dag):
     return api_report_op(dag, dag_config)

def build_email_report_task(dag_config, dag):
     return email_report_op(dag, dag_config)

def build_upload_to_docstore_task(dag_config, dag):
     return upload_docstore_op(dag, dag_config)

def call_task_operators(tasks):
    for i in range(len(tasks) - 1):
        tasks[i] >> tasks[i + 1]

def main():
    
    airflow_dags_config = ROSEY_CONFIG.get('airflow_dags_config')
    for dag_config in airflow_dags_config:
            logging.info(dag_config['dag_name'])
            # try:
            dag_id = generate_dag_id(dag_config)
            dag = generate_dag(dag_id, dag_config)
            execution_type = dag_config.get('execution_type','hive')
            
            if execution_type == 'hive':
                execute_hive_task = build_execute_hive_task(dag_config, dag)
                generate_report_task = build_output_report_task(dag_config, dag)
                tasks = [execute_hive_task,generate_report_task]
            elif execution_type == 'api':
                generate_api_report_task = build_generate_api_report_task(dag_config,dag)
                tasks = [generate_api_report_task]


            if dag_config['tasks_config']['EMAIL_REPORT'].get('email'):
                email_report_task = build_email_report_task(dag_config, dag)
                if dag_config['tasks_config']['UPLOAD_TO_DOCSTORE']['docstore']:
                    upload_docstore_task = build_upload_to_docstore_task(dag_config, dag)
                    tasks.append(upload_docstore_task)
                tasks.append(email_report_task)
                
            
            call_task_operators(tasks)

            logging.info("Running the dag: {}".format(dag_id))
            globals()[dag_id] = dag

            # except BaseException as exception:
            #     logging.error("Exception occurred while scheduling stratos DAGs: {}".format(exception))

main()
