import requests
import json
import urllib3
import os
import csv
import logging
from mcp_reports.dispute_services.utils.stratos_client import StratosClient
from mcp_reports.dispute_services.utils.zencast_client import olympusClient
from mcp_reports.stratos.config.config import STRATOS_BASE_URL
urllib3.disable_warnings()


def get_token():
    token = olympusClient.get_bearer_auth_token()
    return token

def make_api_call(DATA):
    URL = "https://moses.drove.plt.phonepe.mhr/v2/analytics"

    HEADERS = { 'accept': 'application/json',
                'Content-Type': 'application/json'}
    HEADERS['Authorization'] = get_token()
    
    try:

        session = requests.Session()
        http_request = requests.adapters.HTTPAdapter(max_retries=3)
        session.mount('http://', http_request)
        response = session.post(URL, data=json.dumps(DATA), headers=HEADERS, verify=False)
     
        if response.status_code == 200:
            record = response.json()
            logging.info("successfully called make_api_call foxtrot api")
            return record
        else:
            logging.info(response)

    except requests.exceptions.HTTPError as exception: 
        logging.info("Failed to establish connection!! ",exception)
        exit()


def write_header(dag_config):
    columns = dag_config['tasks_config']['API_REPORT'].get('column_header').split(',')
    email_header = "<tr>"
    for column in columns:
        email_header += f'<th>{column}</th>'
    msg_body = f"<table border> {email_header} <th>Date</th> </tr>"
    return msg_body
   
def call_foxtrot(DATA,state_list,dag_config):
    columns = dag_config['tasks_config']['API_REPORT'].get('column').split(',')
    msg_body = write_header(dag_config)
    record = make_api_call(DATA)
    current_states = {}
    terminal_state = []
    for i in state_list:
        for key,value in i.items():
            if key == 'current_state':
                current_states[value] = i['trigger_event']
            elif key == 'terminal_state':
                terminal_state.append(value)
    logging.info(current_states,terminal_state)

    for row in record['documents']:
        rec = row['data']['eventData']
        if rec['fromState'] not in current_states and rec['fromState'] not in terminal_state:
            msg_body += '<tr>'
            for column in columns:
                msg_body += f'<td>{rec[column]}</td>'
            msg_body += f"<td>{row['date']['humanDate']}</td></tr>"
        else:
            transactionReferenceId = rec['transactionReferenceId']
            call_filter_api(transactionReferenceId,current_states)
    
    msg_body += "</table>"
    return msg_body

def call_filter_api(transactionReferenceId,current_states):
    stratos_client = StratosClient()
    response = stratos_client.get_filter_map(transactionReferenceId)
    if response.status_code == 200:
        record = response.json()
        for disputeDetails in record:
            if disputeDetails["currentState"] in current_states:
                disputeWorkflowId = disputeDetails["disputeWorkflowId"]
                disputedAmount = disputeDetails["disputedAmount"]
                logging.info('calling trigger for',transactionReferenceId,disputeWorkflowId,disputedAmount)
                stratos_client.call_trigger(transactionReferenceId,disputeWorkflowId,disputedAmount, current_states[disputeDetails["currentState"]])
