import csv
import imp
import logging
import os
import subprocess
import tempfile
from pathlib import Path

import pendulum
from pybars import Compiler

from mcp_reports.dispute_services.config.config import HDFS_USER_ID, HDFS_CMD, TSV_FILE_NAME_PATTERN, \
    LOCAL_TEMP_DIR, HDFS_TEMP_DIR, TRANSFORMERS_DIR, CONTEXTS_DIR

MKDIR_CMD = """-mkdir -p "{HDFS_DIR_PATH}" """
PUT_CMD = """-put -f "{LOCAL_PATH}" "{HDFS_PATH}" """
GET_CMD = """-get -f "{HDFS_PATH}" "{LOCAL_PATH}" """
RM_CMD = """-rm -r -f "{HDFS_PATH}" """
GETMERGE_CMD = """-getmerge "{HDFS_PATH}" "{LOCAL_PATH}" """


def apply_template(template_data, template_context):
    compiler = Compiler()
    template = compiler.compile(template_data.decode())
    return ''.join(template(template_context))


def determine_execution_date(**kwargs):
    if kwargs['execution_date'].second != 0:
        # Manual run
        execution_date = kwargs['prev_execution_date']
    else:
        # Scheduled or backfill run
        execution_date = kwargs['next_execution_date']

    return execution_date


def get_date_range(execution_date, dag_config):
    days_before_curr_date = dag_config.get('days_before_curr_date', 1)
    end_date = execution_date.subtract(days=days_before_curr_date).end_of('day')

    duration_in_days = dag_config.get('report_duration_in_days', 1)
    start_date = end_date.subtract(days=duration_in_days - 1).start_of('day')

    if 'input_start_date' in dag_config:
        start_date = pendulum.parse(dag_config.get('input_start_date'), format='YYYY-MM-DD')
    if 'input_end_date' in dag_config:
        start_date = pendulum.parse(dag_config.get('input_end_date'), format='YYYY-MM-DD')

    return start_date, end_date


def get_formatted_date(d):
    return d.format('YYYY-MM-DD')


def format_query(query, start_date, end_date, execution_date):
    period = pendulum.period(start_date, end_date)
    month_set = set([d.month for d in period.range('days')])
    year_set = set([d.year for d in period.range('days')])

    context = {
        'YEARS': ",".join(str(year) for year in year_set),
        'MONTHS': ",".join(str(month) for month in month_set),
        'START_DATE': get_formatted_date(start_date),
        'START_TIME': start_date,
        'START_DAY': start_date.day,
        'START_MONTH': start_date.month,
        'START_YEAR': start_date.year,
        'END_DATE': get_formatted_date(end_date),
        'END_TIME': end_date,
        'END_DAY': end_date.day,
        'END_MONTH': end_date.month,
        'END_YEAR': end_date.year,
        'EXECUTION_DATE': get_formatted_date(execution_date),
        'EXECUTION_TIME': execution_date,
        'EXECUTION_DAY': execution_date.day,
        'EXECUTION_MONTH': execution_date.month,
        'EXECUTION_YEAR': execution_date.year
    }

    return apply_template(query.encode(), context)


def read_local_file(file_path):
    with open(file_path, "rb") as fil:
        return fil.read()


def run_cmd(cmd):
    logging.info("Running subprocess: cmd({})".format(cmd))
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True)
    stdout, _ = process.communicate()
    return_code = process.returncode
    if return_code == 0:
        logging.info("Running subprocess completed: stdout({})".format(stdout))
        return stdout
    else:
        raise Exception(
            "Running subprocess failed: return_code({}), stdout({})".format(return_code, stdout))


def append_to_file_top(file_path, content):
    cmd = "echo '{content}' | cat - '{file_path}' > '{temp_path}' && mv '{temp_path}' '{file_path}'".format(
        content=content,
        file_path=file_path,
        temp_path="{}.swp".format(file_path)
    )
    run_cmd(cmd)


def create_dir_for_file(file_path):
    dir_path = os.path.dirname(file_path)
    if not os.path.exists(dir_path):
        os.umask(0)
        os.makedirs(dir_path)


def remove_file_pattern(file_path_pattern):
    cmd = "rm -rf {file_path_pattern}".format(file_path_pattern=file_path_pattern)
    run_cmd(cmd)


def remove_file_pattern_using_airflow_user(file_path_pattern):
    cmd = "sudo -u {airflow_user} rm -rf {file_path_pattern}".format(airflow_user=HDFS_USER_ID,
                                                                     file_path_pattern=
                                                                     file_path_pattern)
    run_cmd(cmd)


def line_count(file_path):
    cmd = "wc -l {file_path}".format(file_path=file_path)
    return int(run_cmd(cmd).decode().strip().split(' ')[0].strip())


def convert_tsv_to_report_format(tsv_file_path, csv_file_path, python_file_name):
    rows = write_units_to_file(tsv_file_path, csv_file_path, python_file_name)
    return rows

def write_units_to_file(tsv_file_path, csv_file_path, python_file_name):
    logging.info("python file path is {}".format(TRANSFORMERS_DIR + "/" + python_file_name))
    processor = imp.load_source("", TRANSFORMERS_DIR + "/" + python_file_name)
    rows = processor.process(tsv_file_path, csv_file_path)
    return rows


def write_units_to_api_file(dag_config,local_report_file_path,python_file_name):
    logging.info("python file path is {}".format(TRANSFORMERS_DIR + "/" + python_file_name))
    processor = imp.load_source("", TRANSFORMERS_DIR + "/" + python_file_name)
    rows = processor.process(dag_config,local_report_file_path)
    return rows

def get_context(python_file_name, csv_file_path, start_date, end_date, execution_date):
    logging.info("python file path is {}".format(CONTEXTS_DIR + "/" + python_file_name))
    processor = imp.load_source("", CONTEXTS_DIR + "/" + python_file_name)
    return processor.build_context(csv_file_path, start_date, end_date, execution_date)

def get_tenant_query_template(query_template):
    query_template = query_template.replace("[[", "{{")
    query_template = query_template.replace("]]", "}}")
    return  query_template

def get_api_file_paths(report_name, file_name, start_date, end_date, execution_date):
    report_file_name = file_name.format(
        START_DATE=get_formatted_date(start_date),
        END_DATE=get_formatted_date(end_date))

    hdfs_report_file_path = HDFS_TEMP_DIR + report_file_name

    local_report_file_path = LOCAL_TEMP_DIR + report_file_name
    return hdfs_report_file_path, local_report_file_path

def get_file_paths(report_name, file_name, start_date, end_date, execution_date, query):
    tsv_file_name = TSV_FILE_NAME_PATTERN.format(REPORT_NAME=report_name,
                                                 START_DATE=get_formatted_date(start_date),
                                                 END_DATE=get_formatted_date(end_date),
                                                 EXECUTION_DATE=get_formatted_date(
                                                     execution_date),
                                                 QUERY=query)
    logging.info(tsv_file_name)
    report_file_name = file_name.format(
        START_DATE=get_formatted_date(start_date),
        END_DATE=get_formatted_date(end_date))

    hdfs_tsv_file_path = HDFS_TEMP_DIR + tsv_file_name
    hdfs_report_file_path = HDFS_TEMP_DIR + report_file_name

    local_tsv_file_path = LOCAL_TEMP_DIR + tsv_file_name
    local_report_file_path = LOCAL_TEMP_DIR + report_file_name

    return hdfs_tsv_file_path, hdfs_report_file_path, local_tsv_file_path, local_report_file_path


def create_hdfs_dir(dir_path):
    logging.info("Creating hdfs directory: dir_path({})".format(dir_path))
    create_dir_cmd = MKDIR_CMD.format(HDFS_DIR_PATH=dir_path)
    run_hdfs_cmd(create_dir_cmd)


def run_hdfs_cmd(cmd, user_id=HDFS_USER_ID):
    logging.info("Running hdfs command: cmd({}), user_id({})".format(cmd, user_id))
    cmd = HDFS_CMD.format(USER_ID=user_id, CMD=cmd)
    logging.info("command is : ({})".format(cmd))
    run_cmd(cmd)


def concat_hdfs_to_local(hdfs_path, local_path, keep_crc=False):
    logging.info("Writing content of hdfs_dir({}) to local_path({})".format(hdfs_path, local_path))
    create_dir_for_file(local_path)
    getmerge_cmd = GETMERGE_CMD.format(HDFS_PATH=hdfs_path, LOCAL_PATH=local_path)
    run_hdfs_cmd(getmerge_cmd)


def delete_from_hdfs(hdfs_path):
    logging.info("Deleting from hdfs: hdfs_path({})".format(hdfs_path))
    delete_cmd = RM_CMD.format(HDFS_PATH=hdfs_path)
    run_hdfs_cmd(delete_cmd)


def concat_local_to_hdfs(local_path, hdfs_path):
    remove_crc_for_file_path(local_path)
    logging.info("Writing content of local_path({}) to hdfs_dir({})".format(local_path, hdfs_path))
    put_cmd = PUT_CMD.format(LOCAL_PATH=local_path, HDFS_PATH=hdfs_path)
    run_hdfs_cmd(put_cmd)


def remove_crc_for_file_path(file_path):
    logging.info("Removing crc file for file_path ({})".format(file_path))
    dir_path = os.path.dirname(file_path)
    file_name = os.path.basename(file_path)
    remove_file_pattern_using_airflow_user("{}/.{}.crc".format(dir_path, file_name))


def get_value(data, column):
    if data.empty is False:
        value = data[column].values[0]
    else:
        value = 0
    return value


def convert_tsv_to_csv(tsv_file_path, csv_file_path) -> None:
    try:
        with open(tsv_file_path, 'r', encoding='utf-8') as tsv_file:
            tsv_reader = csv.reader(tsv_file, delimiter='\t')
            file_contents = list(tsv_reader)

        with open(csv_file_path, 'a', newline='', encoding='utf-8') as csv_file:
            csv_writer = csv.writer(csv_file, delimiter=',', quotechar='|', quoting=csv.QUOTE_MINIMAL)
            csv_writer.writerows(file_contents)

        logging.info(f"Successfully converted TSV to CSV: {tsv_file_path} -> {csv_file_path}")

    except Exception as e:
        error_msg = f"Failed to convert TSV to CSV: {e}"
        logging.error(error_msg)
        raise RuntimeError(error_msg)


def create_empty_csv(filename) -> None:
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        csv.writer(csvfile)
    logging.info(f"Empty CSV file created: {filename}")


def read_file_content(file_path) -> str:
    try:
        with open(file_path, "r", encoding='utf-8') as file:
            return file.read()
    except FileNotFoundError:
        error_msg = f"File not found: {file_path}"
        logging.error(error_msg)
        raise FileNotFoundError(error_msg)
    except Exception as e:
        error_msg = f"Failed to read file {file_path}: {e}"
        logging.error(error_msg)
        raise RuntimeError(error_msg)


def _parse_float_value(value):
    try:
        return float(value.strip())
    except Exception as e:
        raise e


def create_temporary_csv(filename_prefix) -> str:
    try:
        with tempfile.NamedTemporaryFile(
            mode='w',
            delete=False,
            prefix=filename_prefix,
            suffix='.csv',
            newline='',
            encoding='utf-8'
        ) as temp_csv:
            csv.writer(temp_csv)
            temp_file_path = temp_csv.name

        logging.info(f"Temporary CSV file created: {temp_file_path}")
        return temp_file_path

    except Exception as e:
        error_msg = f"Failed to create temporary CSV file with prefix '{filename_prefix}': {e}"
        logging.error(error_msg)
        raise e


def append_row_to_csv(filename, row_data, headers
) -> None:
    try:
        data_row = [row_data.get(header, "") for header in headers]

        with open(filename, 'a', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(data_row)

        logging.debug(f"Row appended to {filename}")

    except Exception as e:
        error_msg = f"Failed to append row to {filename}: {e}"
        logging.error(error_msg)
        raise e


def write_empty_row(file_path, num_columns) -> None:
    empty_row = [''] * num_columns
    with open(file_path, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(empty_row)


def _validate_file_path(file_path) -> Path:
    path = Path(file_path)
    if not path.exists():
        raise FileNotFoundError(f"File does not exist: {file_path}")
    return path

def convert_rs_to_crores(amount_in_rs_str):
    amount = float(amount_in_rs_str)
    return "{:.2f}".format(float(amount / 10000000))

def convert_paise_to_rs(amount_in_paisa_str):
    amount = int(amount_in_paisa_str)
    return int(amount / 100)


def append_to_file_top_safely(file_path, content):
    import tempfile
    import shutil

    try:
        with tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8') as temp_file:
            temp_path = temp_file.name

            temp_file.write(content)

            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as original_file:
                    temp_file.write(original_file.read())

        shutil.move(temp_path, file_path)

    except Exception as e:
        if 'temp_path' in locals() and os.path.exists(temp_path):
            os.unlink(temp_path)
        raise e
