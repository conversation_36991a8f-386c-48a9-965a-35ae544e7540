
from mcp_reports.stratos.config.config import STRATOS_BASE_URL
from retrying import retry
import requests
import logging

from mcp_reports.dispute_services.utils.zencast_client import olympusClient

HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

AUTHORIZATION = "Authorization"


class StratosClient(object):

    @retry(stop_max_attempt_number=3, wait_exponential_multiplier=10, wait_exponential_max=1000)
    def get_enums(self, query_params):
        url = STRATOS_BASE_URL + "/v1/dispute/enums" + query_params


        try:
            auth_token = olympusClient.get_bearer_auth_token()
            if auth_token:
                HEADERS[AUTHORIZATION] = auth_token
                response = requests.get(url=url, headers=HEADERS, verify= False)
                if response.ok :
                    logging.info("Success get enum Stratos response")
                    return response
                else:
                    logging.info("Stratos get enum, Non success response " + str(response.status_code))
                    logging.info(response.content)
            else:
                logging.info("Auth token received from Olympus is empty")
        except Exception as e:
            logging.error("Unable to get Enum from Stratos (will attempt login 3 times): {}".format(e))
            raise e

    @retry(stop_max_attempt_number=3, wait_exponential_multiplier=10, wait_exponential_max=1000)
    def p2pm_reconcile(self, transaction_ref_id, dispute_workflow_id):
        url = STRATOS_BASE_URL + "/v1/toa/P2PM_TOA/reconcile"

        data = {
                  'transactionReferenceId': transaction_ref_id,
                  'disputeWorkflowId': dispute_workflow_id
               }

        try:
            auth_token = olympusClient.get_bearer_auth_token()
            if auth_token:
                HEADERS[AUTHORIZATION] = auth_token
                response = requests.post(url=url, headers=HEADERS, json=data, verify= False)
                if response.ok :
                    logging.info("Success Reconcile Response from Stratos")
                else:
                    logging.info("Stratos Reconcile, Non success Response " + str(response.status_code))
                    logging.info(response.content)
                return response
            else:
                logging.info("Auth token received from Olympus is empty")
        except Exception as e:
            logging.error("Unable to reconcile from Stratos (will attempt login 3 times): {}".format(e))
            raise e

    @retry(stop_max_attempt_number=3, wait_exponential_multiplier=10, wait_exponential_max=1000)
    def toa_reconcile(self, transaction_ref_id, dispute_workflow_id, dispute_type):
        url = STRATOS_BASE_URL + "/v1/toa/"+dispute_type+"/reconcile"
        logging.info("Calling Stratos reconcile Url {}".format(url))
        data = {
            'transactionReferenceId': transaction_ref_id,
            'disputeWorkflowId': dispute_workflow_id
        }

        try:
            auth_token = olympusClient.get_bearer_auth_token()
            if auth_token:
                HEADERS[AUTHORIZATION] = auth_token
                response = requests.post(url=url, headers=HEADERS, json=data, verify=False)
                if response.ok :
                    logging.info("Success Reconcile Response from Stratos")
                else:
                    logging.info("Stratos Reconcile, Non success Response " + str(response.status_code))
                    logging.info(response.content)
                return response
            else:
                logging.info("Auth token received from Olympus is empty")
        except Exception as e:
            logging.error("Unable to reconcile from Stratos (will attempt login 3 times): {}".format(e))
            raise e

        
    @retry(stop_max_attempt_number=3, wait_exponential_multiplier=10, wait_exponential_max=1000)
    def get_filter_map(self, transactionReferenceId):
        url = STRATOS_BASE_URL + "/v1/chargeback/filter"
        logging.info("Calling Stratos filter Url {}".format(url))
        data = {
            "filterType": "TRANSACTION_REFERENCE_ID",
            "transactionReferenceIds":[ f"{transactionReferenceId}"]
        }

        try:
            auth_token = olympusClient.get_bearer_auth_token()
            if auth_token:
                HEADERS[AUTHORIZATION] = auth_token
                response = requests.post(url=url, headers=HEADERS, json=data, verify=False)
                if response.ok :
                    logging.info("Success dispute details Response from Stratos")
                else:
                    logging.info("Stratos dispute details, Non success Response " + str(response.status_code))
                    logging.info(response.content)
                return response
            else:
                logging.info("Auth token received from Olympus is empty")
        except Exception as e:
            logging.error("Unable to get details from Stratos (will attempt login 3 times): {}".format(e))
            raise e

    @retry(stop_max_attempt_number=3, wait_exponential_multiplier=10, wait_exponential_max=1000)
    def call_trigger(self,transactionReferenceId,disputeWorkflowId,disputedAmount, triggerEvent):
        logging.info("Calling trigger API for : "+str(disputeWorkflowId) + " : transactionId : "+str(transactionReferenceId) + " : disputedAmount "+str(disputedAmount))
        url = STRATOS_BASE_URL + "/v1/dispute/"+str(transactionReferenceId)+"/"+str(disputeWorkflowId)+"/trigger/"+str(triggerEvent)
        logging.info("Calling Stratos reconcile Url {}".format(url))
        data = {
            "contextType": "INSTITUTIONAL_CREDIT_CONTEXT",
            "creditSourceType": "FILE",
            "creditAmount": disputedAmount
        }

        try:
            auth_token = olympusClient.get_bearer_auth_token()
            if auth_token:
                HEADERS[AUTHORIZATION] = auth_token
                response = requests.post(url=url, headers=HEADERS, json=data, verify=False)
                if response.ok :
                    logging.info("Success trigger event Response from Stratos")
                else:
                    logging.info("Stratos trigger event, Non success Response " + str(response.status_code))
                    logging.info(response.content)
                return response
            else:
                logging.info("Auth token received from Olympus is empty")
        except Exception as e:
            logging.error("Unable to trigger event from Stratos (will attempt login 3 times): {}".format(e))
            raise e
