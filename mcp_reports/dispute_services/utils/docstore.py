import json
import logging

import requests

from mcp_reports.dispute_services.config.config import OLYMPUS_PROTOCOL, \
    OLYMPUS_HOST, OLYMPUS_PORT, OLYMPUS_CLIENT_ID, \
    OLYMPUS_CLIENT_KEY, NAMESPACE, DOCSTORE_UI_URL, DOCSTORE_BASE_URL, DOCSTORE_TTL
from mcp_reports.dispute_services.utils.olympus_client import OlympusIMClient


def get_request_headers():
    olympus_client = OlympusIMClient(OLYMPUS_PROTOCOL, OLYMPUS_HOST, OLYMPUS_PORT,
                                     OLYMPUS_CLIENT_ID, OLYMPUS_CLIENT_KEY)
    return {
        "Authorization": olympus_client.get_bearer_auth_token()
    }


def download_link(doc_id,alias_id):
    return "{}/downloadFile?docId={}&folderId={}".format(DOCSTORE_UI_URL, doc_id, alias_id)


def upload_file(namespace, fileName, file, alias_id):
    response = requests.post('{}/v2/documents/{}'.format(DOCSTORE_BASE_URL, namespace),
                                files=__build_payload_for_upload_file(namespace,
                                                                        fileName,
                                                                        file,
                                                                        alias_id), headers=get_request_headers(), verify=False)
    if response.status_code == 200:
        record = response.json()
        id = record['context']['id']
        return id

def __build_payload_for_upload_file(namespace, fileName, file, alias_id):
    fileUploadParameter = {}
    fileUploadParameter["namespace"] = namespace
    fileUploadParameter["fileUploadContext"] = {"type": "INTERNAL"}
    fileUploadParameter["meta"] = {"service": "stratos", "fileType": "csv", "fileName": fileName}
    fileUploadParameter["tag"] = ["ytdChargebackSummary"]
    fileUploadParameter["externalContext"] = {"externalRefId": fileName}

    ttlConfigParameter = {}
    ttlConfigParameter["timeToLiveInSec"] = DOCSTORE_TTL
    ttlConfigParameter["priority"] = "LOW"
    fileUploadParameter["ttlConfig"] = ttlConfigParameter
    aliasContextParameter = {}
    aliasContextParameter['aliasIds'] = [alias_id]
    fileUploadParameter['aliasContext'] = aliasContextParameter
    fileParameters = {
        'file': (fileName, file),
        'fileUploadRequest': (None, json.dumps(fileUploadParameter))
    }
    logging.info("file_upload_parameter for upload file: {}".format(fileUploadParameter))
    return fileParameters

def docstore_links(alias, files):
    html = ""
    link = {}
    for file_path in files:
        file_name = file_path.split('/')[-1]
        try:
            logging.info("Generating docstore link for: {}".format(file_path))
            file_id = upload_file(NAMESPACE, file_name, read_local_file(file_path), alias)
            file_link = download_link(file_id, alias)
            link[file_name] = file_link
        except Exception as e:
            logging.error("Unable to generate file link for {}: {}".format(file_path, e))
    for key, value in link.items():
        html += "{} : {}".format(key, value)
    return html

def read_local_file(file_path):
    with open(file_path, "rb") as fil:
        return fil.read()
