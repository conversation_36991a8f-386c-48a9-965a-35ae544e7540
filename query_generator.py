from pybars import Compiler
import logging
from datetime import datetime

import pendulum


def get_formatted_date(d):
    return d.format('YYYY-MM-DD')

DATE_RANGE_CONFIG ={}
DATE_RANGE_CONFIG['report_name']={}
DATE_RANGE_CONFIG['report_name']['MONTH_RANGE_QUERY_BEFORE']=-1
DATE_RANGE_CONFIG['report_name']['DAY_MONTH_RANGE_QUERY_AFTER']=1
def get_month_buffer_range(start_date,end_date,days_range_before_curr_date,days_range_after_curr_date,report_config):
    reporter_config_day_range = DATE_RANGE_CONFIG.get(report_config['name'], {})
    if reporter_config_day_range:
        start_day_range_month =reporter_config_day_range.get('MONTH_RANGE_QUERY_BEFORE',-1)
        end_day_range_month =reporter_config_day_range.get('DAY_MONTH_RANGE_QUERY_AFTER',1)

    start_date_using_range = start_date.add(days=days_range_before_curr_date).end_of('day')
    end_date_using_range = end_date.add(days=days_range_after_curr_date).start_of('day')
    new_start_date_with_month_buffer = start_date_using_range.add(days=start_day_range_month).end_of('day')
    new_end_date_with_month_buffer = end_date_using_range.add(days=end_day_range_month).start_of('day')

    return new_start_date_with_month_buffer,new_end_date_with_month_buffer


def format_query(query, start_date, end_date, execution_date, merchant_id, start_day_range, end_day_range,report_config):
    start_date_buffer_month,end_date_buffer_month =get_month_buffer_range(start_date,end_date,start_day_range,end_day_range,report_config)


    period = pendulum.interval(start_date_buffer_month, end_date_buffer_month)
    month_set = set([d.month for d in period.range('days')])
    year_set = set([d.year for d in period.range('days')])
    transaction_types = set(report_config.get('transactionType', [0]))

    context = {
        'YEARS': ",".join(str(year) for year in year_set),
        'MONTHS': ",".join(str(month) for month in month_set),
        'START_DATE': get_formatted_date(start_date),
        'START_TIME': start_date,
        'START_DAY': start_date.day,
        'START_MONTH': start_date.month,
        'START_YEAR': start_date.year,
        'END_DATE': get_formatted_date(end_date),
        'END_TIME': end_date,
        'END_DAY': end_date.day,
        'END_MONTH': end_date.month,
        'END_YEAR': end_date.year,
        'EXECUTION_DATE': get_formatted_date(execution_date),
        'EXECUTION_TIME': execution_date,
        'EXECUTION_DAY': execution_date.day,
        'EXECUTION_MONTH': execution_date.month,
        'EXECUTION_YEAR': execution_date.year,
        'MERCHANT_ID': merchant_id,
        'BEFORE_DAY_RANGE': start_day_range,
        'AFTER_DAY_RANGE': end_day_range,
        'TRANSACTION_TYPES': ",".join(str(year) for year in transaction_types),
    }

    return apply_template(query.encode(), context)


def apply_template(template_data, template_context):
    compiler = Compiler()
    print("Template data with encode is : {}".format(template_data))
    print("Template context with encode is : {}".format(template_context))
    template = compiler.compile(template_data.decode())
    return ''.join(template(template_context))

if __name__ == '__main__':
    # open text file in read mode
    query ='SELECT settlement_entries.external_transaction_id,merchant_settlement_record.transaction_id, merchant_settlement_record.external_transaction_id, "FORWARD" as transaction_type, settlement_entries.total_payable, merchant_settlement_record.transaction_amount, merchant_settlement_record.transaction_date,settlement_entries.transaction_date FROM torch.merchant_settlement_record AS merchant_settlement_record INNER JOIN (Select max(created) as created, max(transaction_date) as transaction_date, external_transaction_id, sum(total_payable) as total_payable, sum(total_receivable) as total_receivable, sum(amount) as amount, sum(fee) as fee, sum(igst) as igst, sum(cgst) as cgst, sum(sgst) as sgst from accounting.settlement_entries left outer join (SELECT id from valhalla_v2.merchants WHERE merchant_id = \'{{MERCHANT_ID}}\') q1 on settlement_entries.to_party_id = q1.id left outer join  (SELECT id from valhalla_v2.merchants WHERE merchant_id = \'{{MERCHANT_ID}}\') q2 on settlement_entries.from_party_id = q2.id where (q1.id is not null OR q2.id is not null) AND settlement_entries.year in (\'{{YEARS}}\') AND settlement_entries.month in (\'{{MONTHS}}\') AND settlement_entries.transaction_type= \'REDEMPTION\' AND settlement_entries.entry_type = 1 AND settlement_entries.transaction_date >= DATE_ADD(\'{{START_DATE}}\', {{BEFORE_DAY_RANGE}}) AND settlement_entries.transaction_date < DATE_ADD(\'{{END_DATE}}\', \'{{AFTER_DAY_RANGE}}\') AND settlement_entries.event_type != \'MERCHANT_FULFILMENT_REVERSAL\' group by external_transaction_id) settlement_entries ON merchant_settlement_record.external_transaction_id = settlement_entries.external_transaction_id INNER JOIN torch.merchant_settlement AS merchant_settlement ON merchant_settlement_record.merchant_settlement_id = merchant_settlement.id INNER JOIN torch.tenant AS tenant ON merchant_settlement.tenant_id = tenant.id WHERE ABS(merchant_settlement_record.transaction_amount - settlement_entries.total_payable) >= 1 AND merchant_settlement_record.year in (\'{{YEARS}}\') AND merchant_settlement_record.month in (\'{{MONTHS}}\') AND merchant_settlement_record.transaction_type in (\'{{TRANSACTION_TYPES}}\') AND merchant_settlement_record.status != \'24\' AND merchant_settlement_record.transaction_date >= \'{{START_DATE}}\' AND merchant_settlement_record.transaction_date < \'{{END_DATE}}\' AND tenant.provider_name = \'{{MERCHANT_ID}}\''

    # print(query)
    with open('/Users/<USER>/Office/newCode/mcp/lucy-mcp-airflow-dag/mcp_reports/torch/query_templates/bbps/torch_forward.hql', 'r') as file:
        data = file.read().replace('\n', ' ')

        data = data.replace("{{","[[")
        data = data.replace("}}","]]")
        print(data)
        # print(data)
        report_config ={}
        report_config['name']='report_name'
        query_res = format_query(query=data,start_date=pendulum.now(),end_date=pendulum.now(),merchant_id="BBPSBP",execution_date=pendulum.now(),start_day_range=12,end_day_range=12,report_config=report_config)

        print(query_res)
